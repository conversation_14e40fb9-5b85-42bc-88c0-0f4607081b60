import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:banachef/features/referral/widgets/referral_header.dart';

void main() {
  group('ReferralHeader Widget Tests', () {
    testWidgets('should display header with mascot and title', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ReferralHeader(),
          ),
        ),
      );

      // Assert
      expect(find.text('Có ai giới thiệu bạn không?'), findsOneWidget);
      expect(find.byType(Container), findsAtLeastNWidgets(1));
    });

    testWidgets('should be responsive on different screen sizes', (WidgetTester tester) async {
      // Test mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone SE
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ReferralHeader(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Có ai giới thiệu bạn không?'), findsOneWidget);

      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ReferralHeader(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      expect(find.text('Có ai giới thiệu bạn không?'), findsOneWidget);
    });
  });
}
