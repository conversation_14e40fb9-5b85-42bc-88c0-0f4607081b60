import 'package:flutter/material.dart';
import 'bana_colors.dart';

/// BanaChef AI Typography System
/// 
/// Comprehensive typography system following the BanaChef Design System.
/// Based on SF Pro font family with carefully crafted scales and hierarchy.
/// 
/// Usage:
/// ```dart
/// Text(
///   'Welcome to BanaChef',
///   style: BanaTypography.title1,
/// )
/// 
/// // Or use with context extensions
/// Text(
///   'Recipe Title',
///   style: context.title2,
/// )
/// ```
class BanaTypography {
  BanaTypography._(); // Private constructor

  // ============================================================================
  // FONT FAMILIES
  // ============================================================================
  
  /// Primary font family - SF Pro
  /// Used for all text in the app
  static const String fontFamily = 'SF Pro';
  
  /// Fallback font families for different platforms
  static const List<String> fontFamilyFallback = [
    'SF Pro Display',
    'SF Pro Text',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
  ];

  // ============================================================================
  // FONT WEIGHTS
  // ============================================================================
  
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semibold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight heavy = FontWeight.w800;

  // ============================================================================
  // DISPLAY STYLES (Large Titles)
  // ============================================================================
  
  /// Title 1 - 34pt, Bold
  /// Use for: Main screen titles, hero headlines
  static const TextStyle title1 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 34.0,
    fontWeight: bold,
    height: 1.2, // Line height: 40.8pt
    letterSpacing: -0.5,
    color: BanaColors.text,
  );
  
  /// Title 2 - 28pt, Bold
  /// Use for: Section titles, major headings
  static const TextStyle title2 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28.0,
    fontWeight: bold,
    height: 1.25, // Line height: 35pt
    letterSpacing: -0.3,
    color: BanaColors.text,
  );
  
  /// Title 3 - 22pt, Semibold
  /// Use for: Card titles, important headings
  static const TextStyle title3 = TextStyle(
    fontFamily: fontFamily,
    fontSize: 22.0,
    fontWeight: semibold,
    height: 1.3, // Line height: 28.6pt
    letterSpacing: -0.2,
    color: BanaColors.text,
  );

  // ============================================================================
  // HEADLINE STYLES
  // ============================================================================
  
  /// Headline - 17pt, Semibold
  /// Use for: Recipe names in lists, important labels
  static const TextStyle headline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 17.0,
    fontWeight: semibold,
    height: 1.4, // Line height: 23.8pt
    letterSpacing: -0.1,
    color: BanaColors.text,
  );

  // ============================================================================
  // BODY STYLES
  // ============================================================================
  
  /// Body Large - 17pt, Regular
  /// Use for: Main content text, descriptions
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 17.0,
    fontWeight: regular,
    height: 1.5, // Line height: 25.5pt
    letterSpacing: 0.0,
    color: BanaColors.text,
  );
  
  /// Body Medium - 15pt, Regular
  /// Use for: Secondary content, form labels
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 15.0,
    fontWeight: regular,
    height: 1.4, // Line height: 21pt
    letterSpacing: 0.0,
    color: BanaColors.text,
  );
  
  /// Body Small - 13pt, Regular
  /// Use for: Captions, metadata, fine print
  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13.0,
    fontWeight: regular,
    height: 1.4, // Line height: 18.2pt
    letterSpacing: 0.0,
    color: BanaColors.textSecondary,
  );

  // ============================================================================
  // LABEL STYLES
  // ============================================================================
  
  /// Label Large - 16pt, Regular
  /// Use for: Form field labels, section headers
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: regular,
    height: 1.3, // Line height: 20.8pt
    letterSpacing: 0.1,
    color: BanaColors.text,
  );
  
  /// Label Medium - 14pt, Medium
  /// Use for: Button labels, tab labels
  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    height: 1.3, // Line height: 18.2pt
    letterSpacing: 0.1,
    color: BanaColors.text,
  );
  
  /// Label Small - 12pt, Medium
  /// Use for: Small button labels, badges
  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: medium,
    height: 1.3, // Line height: 15.6pt
    letterSpacing: 0.2,
    color: BanaColors.textSecondary,
  );

  // ============================================================================
  // BUTTON STYLES
  // ============================================================================
  
  /// Button Large - 17pt, Semibold
  /// Use for: Primary buttons, important CTAs
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 17.0,
    fontWeight: semibold,
    height: 1.2, // Line height: 20.4pt
    letterSpacing: 0.0,
    color: BanaColors.onPrimary,
  );
  
  /// Button Medium - 15pt, Semibold
  /// Use for: Secondary buttons, medium CTAs
  static const TextStyle buttonMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 15.0,
    fontWeight: semibold,
    height: 1.2, // Line height: 18pt
    letterSpacing: 0.0,
    color: BanaColors.text,
  );
  
  /// Button Small - 13pt, Semibold
  /// Use for: Small buttons, compact CTAs
  static const TextStyle buttonSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13.0,
    fontWeight: semibold,
    height: 1.2, // Line height: 15.6pt
    letterSpacing: 0.1,
    color: BanaColors.text,
  );

  // ============================================================================
  // SPECIALIZED STYLES
  // ============================================================================
  
  /// Caption - 13pt, Regular
  /// Use for: Image captions, timestamps, metadata
  static const TextStyle caption = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13.0,
    fontWeight: regular,
    height: 1.3, // Line height: 16.9pt
    letterSpacing: 0.0,
    color: BanaColors.textSecondary,
  );
  
  /// Overline - 11pt, Medium, Uppercase
  /// Use for: Section labels, category tags
  static const TextStyle overline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 11.0,
    fontWeight: medium,
    height: 1.3, // Line height: 14.3pt
    letterSpacing: 1.0,
    color: BanaColors.textSecondary,
  );

  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  /// Create a text style with custom color
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  /// Create a text style with custom weight
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
  
  /// Create a text style with custom size
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
  
  /// Create emphasized version of a text style
  static TextStyle emphasized(TextStyle style) {
    return style.copyWith(fontWeight: semibold);
  }
  
  /// Create muted version of a text style
  static TextStyle muted(TextStyle style) {
    return style.copyWith(color: BanaColors.textSecondary);
  }
}
