import 'package:flutter/material.dart';

/// BanaChef AI Shadow & Elevation System
/// 
/// Comprehensive shadow system following the BanaChef Design System.
/// Provides consistent elevation and depth hierarchy for UI components.
/// 
/// Usage:
/// ```dart
/// Container(
///   decoration: BoxDecoration(
///     boxShadow: BanaShadows.elevation1,
///     borderRadius: BorderRadius.circular(12),
///   ),
///   child: Card(...),
/// )
/// ```
class BanaShadows {
  BanaShadows._(); // Private constructor

  // ============================================================================
  // ELEVATION LEVELS
  // ============================================================================
  
  /// No elevation - No shadow
  /// Use for: Flat elements, backgrounds
  static const List<BoxShadow> none = [];
  
  /// Elevation 1 - Very subtle shadow
  /// Use for: Static cards, resting elements
  /// Equivalent to Material Design elevation 1dp
  static const List<BoxShadow> elevation1 = [
    BoxShadow(
      color: Color(0x1A000000), // 10% black
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];
  
  /// Elevation 2 - Light shadow
  /// Use for: Clickable cards, buttons, interactive elements
  /// Equivalent to Material Design elevation 2dp
  static const List<BoxShadow> elevation2 = [
    BoxShadow(
      color: Color(0x1F000000), // 12% black
      offset: Offset(0, 2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
  ];
  
  /// Elevation 3 - Medium shadow
  /// Use for: Raised buttons, floating elements
  /// Equivalent to Material Design elevation 4dp
  static const List<BoxShadow> elevation3 = [
    BoxShadow(
      color: Color(0x24000000), // 14% black
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];
  
  /// Elevation 4 - Strong shadow
  /// Use for: Modals, dropdowns, overlays
  /// Equivalent to Material Design elevation 8dp
  static const List<BoxShadow> elevation4 = [
    BoxShadow(
      color: Color(0x29000000), // 16% black
      offset: Offset(0, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];
  
  /// Elevation 5 - Very strong shadow
  /// Use for: Navigation drawers, major overlays
  /// Equivalent to Material Design elevation 16dp
  static const List<BoxShadow> elevation5 = [
    BoxShadow(
      color: Color(0x33000000), // 20% black
      offset: Offset(0, 12),
      blurRadius: 24,
      spreadRadius: 0,
    ),
  ];

  // ============================================================================
  // SPECIALIZED SHADOWS
  // ============================================================================
  
  /// Button shadow - For elevated buttons
  /// Optimized for button components with subtle depth
  static const List<BoxShadow> button = [
    BoxShadow(
      color: Color(0x1A000000), // 10% black
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];
  
  /// Button pressed shadow - For pressed button state
  /// Reduced shadow to indicate pressed state
  static const List<BoxShadow> buttonPressed = [
    BoxShadow(
      color: Color(0x14000000), // 8% black
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  /// Card shadow - For recipe cards and content cards
  /// Optimized for card components with clean appearance
  static const List<BoxShadow> card = [
    BoxShadow(
      color: Color(0x1A000000), // 10% black
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];
  
  /// Card hover shadow - For interactive cards on hover
  /// Enhanced shadow for hover state
  static const List<BoxShadow> cardHover = [
    BoxShadow(
      color: Color(0x24000000), // 14% black
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  /// FAB shadow - For Floating Action Button
  /// Strong shadow to emphasize floating nature
  static const List<BoxShadow> fab = [
    BoxShadow(
      color: Color(0x29000000), // 16% black
      offset: Offset(0, 6),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  /// Modal shadow - For modal dialogs and overlays
  /// Strong shadow to separate from background
  static const List<BoxShadow> modal = [
    BoxShadow(
      color: Color(0x33000000), // 20% black
      offset: Offset(0, 16),
      blurRadius: 32,
      spreadRadius: 0,
    ),
  ];
  
  /// Bottom sheet shadow - For bottom sheets
  /// Upward shadow for bottom sheets
  static const List<BoxShadow> bottomSheet = [
    BoxShadow(
      color: Color(0x29000000), // 16% black
      offset: Offset(0, -4),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];
  
  /// App bar shadow - For app bars and navigation
  /// Subtle downward shadow
  static const List<BoxShadow> appBar = [
    BoxShadow(
      color: Color(0x1A000000), // 10% black
      offset: Offset(0, 1),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // ============================================================================
  // COLORED SHADOWS
  // ============================================================================
  
  /// Primary colored shadow - Using brand color
  /// Use for: Primary buttons, key elements
  static const List<BoxShadow> primaryColored = [
    BoxShadow(
      color: Color(0x33FFD15C), // 20% primary color
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  /// Success colored shadow - Using success color
  /// Use for: Success buttons, positive actions
  static const List<BoxShadow> successColored = [
    BoxShadow(
      color: Color(0x336ECB63), // 20% success color
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  /// Error colored shadow - Using error color
  /// Use for: Error states, destructive actions
  static const List<BoxShadow> errorColored = [
    BoxShadow(
      color: Color(0x33DC3545), // 20% error color
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Create custom shadow with specified parameters
  static List<BoxShadow> custom({
    required Color color,
    required Offset offset,
    required double blurRadius,
    double spreadRadius = 0,
  }) {
    return [
      BoxShadow(
        color: color,
        offset: offset,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  /// Create shadow with custom opacity
  static List<BoxShadow> withOpacity(List<BoxShadow> shadows, double opacity) {
    return shadows.map((shadow) {
      return shadow.copyWith(
        color: shadow.color.withValues(alpha: opacity),
      );
    }).toList();
  }
  
  /// Create shadow with custom color
  static List<BoxShadow> withColor(List<BoxShadow> shadows, Color color) {
    return shadows.map((shadow) {
      return shadow.copyWith(color: color);
    }).toList();
  }
  
  /// Get elevation shadow by level (0-5)
  static List<BoxShadow> getElevation(int level) {
    switch (level) {
      case 0:
        return none;
      case 1:
        return elevation1;
      case 2:
        return elevation2;
      case 3:
        return elevation3;
      case 4:
        return elevation4;
      case 5:
        return elevation5;
      default:
        return elevation2; // Default to elevation 2
    }
  }
}
