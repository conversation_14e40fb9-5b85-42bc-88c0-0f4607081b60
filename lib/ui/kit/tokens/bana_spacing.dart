import 'package:flutter/material.dart';

/// BanaChef AI Spacing System
/// 
/// Comprehensive spacing system based on 8pt grid following the BanaChef Design System.
/// Provides consistent spacing values and utilities for layouts.
/// 
/// Usage:
/// ```dart
/// Padding(
///   padding: BanaSpacing.all.lg,
///   child: Column(
///     children: [
///       Text('Title'),
///       BanaSpacing.vertical.md,
///       Text('Content'),
///     ],
///   ),
/// )
/// ```
class BanaSpacing {
  BanaSpacing._(); // Private constructor

  // ============================================================================
  // BASE SPACING VALUES (8pt Grid)
  // ============================================================================
  
  /// Extra small spacing - 4pt
  /// Use for: Very tight spacing, icon padding
  static const double xs = 4.0;
  
  /// Small spacing - 8pt
  /// Use for: Minimal spacing, compact layouts
  static const double sm = 8.0;
  
  /// Medium spacing - 16pt
  /// Use for: Standard component padding
  static const double md = 16.0;
  
  /// Large spacing - 24pt
  /// Use for: Standard margin between components
  static const double lg = 24.0;
  
  /// Extra large spacing - 32pt
  /// Use for: Large margins, section spacing
  static const double xl = 32.0;
  
  /// Extra extra large spacing - 40pt
  /// Use for: Major section spacing, page margins
  static const double xxl = 40.0;
  
  /// Extra extra extra large spacing - 48pt
  /// Use for: Maximum spacing, hero sections
  static const double xxxl = 48.0;

  // ============================================================================
  // SEMANTIC SPACING VALUES
  // ============================================================================
  
  /// Page horizontal padding
  /// Use for: Main page content padding
  static const double pagePadding = md;
  
  /// Card padding
  /// Use for: Internal card content padding
  static const double cardPadding = md;
  
  /// Button padding horizontal
  /// Use for: Button internal horizontal padding
  static const double buttonPaddingHorizontal = lg;
  
  /// Button padding vertical
  /// Use for: Button internal vertical padding
  static const double buttonPaddingVertical = md;
  
  /// Input field padding
  /// Use for: Text field internal padding
  static const double inputPadding = md;
  
  /// List item padding
  /// Use for: List item internal padding
  static const double listItemPadding = md;
  
  /// Section spacing
  /// Use for: Spacing between major sections
  static const double sectionSpacing = xl;

  // ============================================================================
  // EDGE INSETS UTILITIES
  // ============================================================================
  
  /// All-direction padding utilities
  static const EdgeInsetsAll all = EdgeInsetsAll._();
  
  /// Horizontal padding utilities
  static const EdgeInsetsHorizontal horizontal = EdgeInsetsHorizontal._();
  
  /// Vertical padding utilities
  static const EdgeInsetsVertical vertical = EdgeInsetsVertical._();
  
  /// Top padding utilities
  static const EdgeInsetsTop top = EdgeInsetsTop._();
  
  /// Bottom padding utilities
  static const EdgeInsetsBottom bottom = EdgeInsetsBottom._();
  
  /// Left padding utilities
  static const EdgeInsetsLeft left = EdgeInsetsLeft._();
  
  /// Right padding utilities
  static const EdgeInsetsRight right = EdgeInsetsRight._();

  // ============================================================================
  // SIZED BOX UTILITIES
  // ============================================================================
  
  /// Vertical spacing widgets
  static const VerticalSpacing verticalSpacing = VerticalSpacing._();
  
  /// Horizontal spacing widgets
  static const HorizontalSpacing horizontalSpacing = HorizontalSpacing._();
}

// ============================================================================
// EDGE INSETS UTILITY CLASSES
// ============================================================================

/// All-direction padding utilities
class EdgeInsetsAll {
  const EdgeInsetsAll._();
  
  EdgeInsets get xs => const EdgeInsets.all(BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.all(BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.all(BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.all(BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.all(BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.all(BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.all(BanaSpacing.xxxl);
}

/// Horizontal padding utilities
class EdgeInsetsHorizontal {
  const EdgeInsetsHorizontal._();
  
  EdgeInsets get xs => const EdgeInsets.symmetric(horizontal: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.symmetric(horizontal: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.symmetric(horizontal: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.symmetric(horizontal: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.symmetric(horizontal: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.symmetric(horizontal: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.symmetric(horizontal: BanaSpacing.xxxl);
}

/// Vertical padding utilities
class EdgeInsetsVertical {
  const EdgeInsetsVertical._();
  
  EdgeInsets get xs => const EdgeInsets.symmetric(vertical: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.symmetric(vertical: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.symmetric(vertical: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.symmetric(vertical: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.symmetric(vertical: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.symmetric(vertical: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.symmetric(vertical: BanaSpacing.xxxl);
}

/// Top padding utilities
class EdgeInsetsTop {
  const EdgeInsetsTop._();
  
  EdgeInsets get xs => const EdgeInsets.only(top: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.only(top: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.only(top: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.only(top: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.only(top: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.only(top: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.only(top: BanaSpacing.xxxl);
}

/// Bottom padding utilities
class EdgeInsetsBottom {
  const EdgeInsetsBottom._();
  
  EdgeInsets get xs => const EdgeInsets.only(bottom: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.only(bottom: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.only(bottom: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.only(bottom: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.only(bottom: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.only(bottom: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.only(bottom: BanaSpacing.xxxl);
}

/// Left padding utilities
class EdgeInsetsLeft {
  const EdgeInsetsLeft._();
  
  EdgeInsets get xs => const EdgeInsets.only(left: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.only(left: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.only(left: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.only(left: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.only(left: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.only(left: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.only(left: BanaSpacing.xxxl);
}

/// Right padding utilities
class EdgeInsetsRight {
  const EdgeInsetsRight._();
  
  EdgeInsets get xs => const EdgeInsets.only(right: BanaSpacing.xs);
  EdgeInsets get sm => const EdgeInsets.only(right: BanaSpacing.sm);
  EdgeInsets get md => const EdgeInsets.only(right: BanaSpacing.md);
  EdgeInsets get lg => const EdgeInsets.only(right: BanaSpacing.lg);
  EdgeInsets get xl => const EdgeInsets.only(right: BanaSpacing.xl);
  EdgeInsets get xxl => const EdgeInsets.only(right: BanaSpacing.xxl);
  EdgeInsets get xxxl => const EdgeInsets.only(right: BanaSpacing.xxxl);
}

// ============================================================================
// SIZED BOX UTILITY CLASSES
// ============================================================================

/// Vertical spacing widgets
class VerticalSpacing {
  const VerticalSpacing._();
  
  Widget get xs => const SizedBox(height: BanaSpacing.xs);
  Widget get sm => const SizedBox(height: BanaSpacing.sm);
  Widget get md => const SizedBox(height: BanaSpacing.md);
  Widget get lg => const SizedBox(height: BanaSpacing.lg);
  Widget get xl => const SizedBox(height: BanaSpacing.xl);
  Widget get xxl => const SizedBox(height: BanaSpacing.xxl);
  Widget get xxxl => const SizedBox(height: BanaSpacing.xxxl);
}

/// Horizontal spacing widgets
class HorizontalSpacing {
  const HorizontalSpacing._();
  
  Widget get xs => const SizedBox(width: BanaSpacing.xs);
  Widget get sm => const SizedBox(width: BanaSpacing.sm);
  Widget get md => const SizedBox(width: BanaSpacing.md);
  Widget get lg => const SizedBox(width: BanaSpacing.lg);
  Widget get xl => const SizedBox(width: BanaSpacing.xl);
  Widget get xxl => const SizedBox(width: BanaSpacing.xxl);
  Widget get xxxl => const SizedBox(width: BanaSpacing.xxxl);
}
