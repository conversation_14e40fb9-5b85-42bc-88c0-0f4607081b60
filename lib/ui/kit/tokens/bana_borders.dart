import 'package:flutter/material.dart';
import 'bana_colors.dart';

/// BanaChef AI Border & Border Radius System
/// 
/// Comprehensive border system following the BanaChef Design System.
/// Provides consistent border radius values and border styles for UI components.
/// 
/// Usage:
/// ```dart
/// Container(
///   decoration: BoxDecoration(
///     borderRadius: BanaBorders.radius.lg,
///     border: BanaBorders.all.medium,
///   ),
///   child: Content(...),
/// )
/// ```
class BanaBorders {
  BanaBorders._(); // Private constructor

  // ============================================================================
  // BORDER RADIUS VALUES
  // ============================================================================
  
  /// Border radius utilities
  static const BorderRadiusValues radius = BorderRadiusValues._();
  
  /// Border utilities
  static const BorderValues all = BorderValues._();
  
  /// Top border utilities
  static const TopBorderValues top = TopBorderValues._();
  
  /// Bottom border utilities
  static const BottomBorderValues bottom = BottomBorderValues._();
  
  /// Left border utilities
  static const LeftBorderValues left = LeftBorderValues._();
  
  /// Right border utilities
  static const RightBorderValues right = RightBorderValues._();

  // ============================================================================
  // BORDER RADIUS CONSTANTS
  // ============================================================================
  
  /// No border radius
  static const double radiusNone = 0.0;
  
  /// Small border radius - 4pt
  /// Use for: Small elements, badges
  static const double radiusXS = 4.0;
  
  /// Small border radius - 8pt
  /// Use for: Buttons, small cards
  static const double radiusSM = 8.0;
  
  /// Medium border radius - 12pt
  /// Use for: Standard buttons, input fields
  static const double radiusMD = 12.0;
  
  /// Large border radius - 16pt
  /// Use for: Cards, larger components
  static const double radiusLG = 16.0;
  
  /// Extra large border radius - 20pt
  /// Use for: Large cards, modals
  static const double radiusXL = 20.0;
  
  /// Extra extra large border radius - 24pt
  /// Use for: Hero sections, major components
  static const double radiusXXL = 24.0;
  
  /// Circular border radius - 999pt
  /// Use for: Circular buttons, avatars
  static const double radiusCircular = 999.0;

  // ============================================================================
  // BORDER WIDTH CONSTANTS
  // ============================================================================
  
  /// Thin border width - 1pt
  /// Use for: Standard borders, dividers
  static const double widthThin = 1.0;
  
  /// Medium border width - 1.5pt
  /// Use for: Emphasized borders, focused states
  static const double widthMedium = 1.5;
  
  /// Thick border width - 2pt
  /// Use for: Strong emphasis, error states
  static const double widthThick = 2.0;
  
  /// Extra thick border width - 3pt
  /// Use for: Maximum emphasis, special states
  static const double widthExtraThick = 3.0;

  // ============================================================================
  // SPECIALIZED BORDER RADIUS
  // ============================================================================
  
  /// Button border radius
  static const double buttonRadius = radiusMD;
  
  /// Card border radius
  static const double cardRadius = radiusLG;
  
  /// Input field border radius
  static const double inputRadius = radiusMD;
  
  /// Modal border radius
  static const double modalRadius = radiusXL;
  
  /// Bottom sheet border radius (top corners only)
  static const double bottomSheetRadius = radiusXL;
  
  /// FAB border radius
  static const double fabRadius = radiusCircular;
}

// ============================================================================
// BORDER RADIUS UTILITY CLASSES
// ============================================================================

/// Border radius utilities
class BorderRadiusValues {
  const BorderRadiusValues._();
  
  BorderRadius get none => BorderRadius.circular(BanaBorders.radiusNone);
  BorderRadius get xs => BorderRadius.circular(BanaBorders.radiusXS);
  BorderRadius get sm => BorderRadius.circular(BanaBorders.radiusSM);
  BorderRadius get md => BorderRadius.circular(BanaBorders.radiusMD);
  BorderRadius get lg => BorderRadius.circular(BanaBorders.radiusLG);
  BorderRadius get xl => BorderRadius.circular(BanaBorders.radiusXL);
  BorderRadius get xxl => BorderRadius.circular(BanaBorders.radiusXXL);
  BorderRadius get circular => BorderRadius.circular(BanaBorders.radiusCircular);
  
  /// Top-only border radius
  BorderRadius get topXS => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusXS));
  BorderRadius get topSM => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusSM));
  BorderRadius get topMD => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusMD));
  BorderRadius get topLG => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusLG));
  BorderRadius get topXL => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusXL));
  BorderRadius get topXXL => BorderRadius.vertical(top: Radius.circular(BanaBorders.radiusXXL));
  
  /// Bottom-only border radius
  BorderRadius get bottomXS => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusXS));
  BorderRadius get bottomSM => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusSM));
  BorderRadius get bottomMD => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusMD));
  BorderRadius get bottomLG => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusLG));
  BorderRadius get bottomXL => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusXL));
  BorderRadius get bottomXXL => BorderRadius.vertical(bottom: Radius.circular(BanaBorders.radiusXXL));
  
  /// Left-only border radius
  BorderRadius get leftXS => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusXS));
  BorderRadius get leftSM => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusSM));
  BorderRadius get leftMD => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusMD));
  BorderRadius get leftLG => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusLG));
  BorderRadius get leftXL => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusXL));
  BorderRadius get leftXXL => BorderRadius.horizontal(left: Radius.circular(BanaBorders.radiusXXL));
  
  /// Right-only border radius
  BorderRadius get rightXS => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusXS));
  BorderRadius get rightSM => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusSM));
  BorderRadius get rightMD => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusMD));
  BorderRadius get rightLG => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusLG));
  BorderRadius get rightXL => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusXL));
  BorderRadius get rightXXL => BorderRadius.horizontal(right: Radius.circular(BanaBorders.radiusXXL));
}

// ============================================================================
// BORDER UTILITY CLASSES
// ============================================================================

/// All-direction border utilities
class BorderValues {
  const BorderValues._();
  
  Border get thin => Border.all(width: BanaBorders.widthThin, color: BanaColors.border);
  Border get medium => Border.all(width: BanaBorders.widthMedium, color: BanaColors.border);
  Border get thick => Border.all(width: BanaBorders.widthThick, color: BanaColors.border);
  Border get extraThick => Border.all(width: BanaBorders.widthExtraThick, color: BanaColors.border);
  
  /// Colored borders
  Border get primary => Border.all(width: BanaBorders.widthMedium, color: BanaColors.primary);
  Border get success => Border.all(width: BanaBorders.widthMedium, color: BanaColors.success);
  Border get error => Border.all(width: BanaBorders.widthMedium, color: BanaColors.error);
  Border get warning => Border.all(width: BanaBorders.widthMedium, color: BanaColors.warning);
  
  /// Custom border with color
  Border withColor(Color color) => Border.all(width: BanaBorders.widthMedium, color: color);
  Border thinWithColor(Color color) => Border.all(width: BanaBorders.widthThin, color: color);
  Border thickWithColor(Color color) => Border.all(width: BanaBorders.widthThick, color: color);
}

/// Top border utilities
class TopBorderValues {
  const TopBorderValues._();
  
  Border get thin => Border(top: BorderSide(width: BanaBorders.widthThin, color: BanaColors.border));
  Border get medium => Border(top: BorderSide(width: BanaBorders.widthMedium, color: BanaColors.border));
  Border get thick => Border(top: BorderSide(width: BanaBorders.widthThick, color: BanaColors.border));
  
  Border withColor(Color color) => Border(top: BorderSide(width: BanaBorders.widthMedium, color: color));
}

/// Bottom border utilities
class BottomBorderValues {
  const BottomBorderValues._();
  
  Border get thin => Border(bottom: BorderSide(width: BanaBorders.widthThin, color: BanaColors.border));
  Border get medium => Border(bottom: BorderSide(width: BanaBorders.widthMedium, color: BanaColors.border));
  Border get thick => Border(bottom: BorderSide(width: BanaBorders.widthThick, color: BanaColors.border));
  
  Border withColor(Color color) => Border(bottom: BorderSide(width: BanaBorders.widthMedium, color: color));
}

/// Left border utilities
class LeftBorderValues {
  const LeftBorderValues._();
  
  Border get thin => Border(left: BorderSide(width: BanaBorders.widthThin, color: BanaColors.border));
  Border get medium => Border(left: BorderSide(width: BanaBorders.widthMedium, color: BanaColors.border));
  Border get thick => Border(left: BorderSide(width: BanaBorders.widthThick, color: BanaColors.border));
  
  Border withColor(Color color) => Border(left: BorderSide(width: BanaBorders.widthMedium, color: color));
}

/// Right border utilities
class RightBorderValues {
  const RightBorderValues._();
  
  Border get thin => Border(right: BorderSide(width: BanaBorders.widthThin, color: BanaColors.border));
  Border get medium => Border(right: BorderSide(width: BanaBorders.widthMedium, color: BanaColors.border));
  Border get thick => Border(right: BorderSide(width: BanaBorders.widthThick, color: BanaColors.border));
  
  Border withColor(Color color) => Border(right: BorderSide(width: BanaBorders.widthMedium, color: color));
}
