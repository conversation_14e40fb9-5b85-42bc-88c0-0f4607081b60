# 🎨 BanaChef AI UI Kit

Comprehensive UI component library following the BanaChef Design System. This kit provides all the building blocks needed to create consistent, accessible, and beautiful user interfaces.

## 🚀 Quick Start

```dart
import 'package:banachef/ui/kit/ui_kit.dart';

// Use any component with design system compliance
BanaButton.primary(
  text: 'Get Started',
  onPressed: () {},
)
```

## 📁 Structure

```
lib/ui/kit/
├── 📄 ui_kit.dart                    # Main export file
├── 📄 README.md                      # This documentation
├── 📁 tokens/                        # Design tokens
│   ├── bana_colors.dart             # Color palette
│   ├── bana_typography.dart         # Typography system
│   ├── bana_spacing.dart            # Spacing system
│   ├── bana_shadows.dart            # Shadow/elevation system
│   └── bana_borders.dart            # Border radius system
├── 📁 components/                    # UI Components
│   ├── 📁 buttons/                  # Button components
│   │   ├── bana_button.dart         # Primary, Secondary, Text buttons
│   │   ├── bana_icon_button.dart    # Icon buttons
│   │   └── bana_fab.dart            # Floating Action Button
│   ├── 📁 cards/                    # Card components
│   │   ├── bana_recipe_card.dart    # Recipe display cards
│   │   ├── bana_category_card.dart  # Category cards
│   │   └── bana_pantry_card.dart    # Pantry item cards
│   ├── 📁 inputs/                   # Input components
│   │   ├── bana_text_field.dart     # Text input fields
│   │   ├── bana_search_bar.dart     # Search input
│   │   ├── bana_segmented_control.dart # Segmented controls
│   │   └── bana_selection_controls.dart # Checkboxes, toggles
│   ├── 📁 overlays/                 # Overlay components
│   │   ├── bana_modal.dart          # Modal dialogs
│   │   ├── bana_bottom_sheet.dart   # Bottom sheets
│   │   └── bana_toast.dart          # Toast notifications
│   ├── 📁 icons/                    # Icon system
│   │   └── bana_icons.dart          # Icon library
│   └── 📁 layout/                   # Layout components
│       ├── bana_scaffold.dart       # App scaffold
│       ├── bana_app_bar.dart        # App bar
│       └── bana_navigation.dart     # Navigation components
└── 📁 utils/                        # Utilities
    ├── bana_animations.dart         # Animation utilities
    ├── bana_accessibility.dart      # Accessibility helpers
    └── bana_theme_extensions.dart   # Theme extensions
```

## 🎯 Design Principles

### Simple
- Prioritize whitespace
- One primary action per screen
- Clear visual hierarchy

### Human & Helpful
- Friendly, encouraging tone
- Human-centered design
- Empathetic error handling

### Inspiring
- High-quality food imagery
- Intuitive interactions
- Delightful micro-interactions

## 🎨 Design Tokens

### Colors
- **Primary**: Banana Yellow (#FFD15C)
- **Text**: Deep Charcoal (#2D2D2D)
- **Background**: Pure White (#FFFFFF), Light Grey (#F5F5F5)
- **System**: Success (#28A745), Error (#DC3545), Warning (#FFC107)

### Typography
- **Font Family**: SF Pro (Display & Text)
- **Scale**: Title 1 (34pt) → Caption (13pt)
- **Line Height**: 1.4x - 1.5x font size

### Spacing
- **Grid**: 8pt base grid system
- **Scale**: 8pt, 16pt, 24pt, 32pt, 40pt

## 🧩 Component Categories

### Buttons
- **Primary**: Main call-to-action
- **Secondary**: Secondary actions
- **Text**: Low-priority actions
- **Icon**: Icon-only actions
- **FAB**: Floating action button

### Cards
- **Recipe Card**: Display recipes with image, title, metadata
- **Category Card**: Represent food categories
- **Pantry Card**: Show pantry items with actions

### Inputs
- **Text Field**: Text input with validation
- **Search Bar**: Search functionality
- **Segmented Control**: Mutually exclusive options
- **Selection Controls**: Checkboxes, toggles

### Overlays
- **Modal**: Critical information/actions
- **Bottom Sheet**: Contextual options
- **Toast**: Quick feedback messages

## ♿ Accessibility

All components follow WCAG 2.1 AA standards:
- **VoiceOver support**: Proper accessibility labels
- **Dynamic Type**: Supports iOS text scaling
- **Touch targets**: Minimum 44pt touch areas
- **Color contrast**: WCAG AA compliant
- **Reduce Motion**: Respects motion preferences

## 🎬 Animations

### Principles
- **Responsive & Informative**: Direct feedback to user actions
- **Cohesive & Guiding**: Show relationships between screens
- **Celebratory & Fun**: Reflect BanaChef's joyful spirit

### Timing
- **Fast (100-200ms)**: Immediate feedback
- **Medium (200-350ms)**: Component animations
- **Slow (350-500ms)**: Screen transitions

## 📱 Responsive Design

All components are responsive and work across:
- **Mobile**: < 600px (Bottom navigation, 1.0x scale)
- **Tablet**: 600-1024px (Side navigation, 1.25x scale)
- **Desktop**: > 1024px (Side navigation, 1.5x scale)

## 🔧 Usage Examples

See individual component files for detailed usage examples and API documentation.

## 🤝 Contributing

When adding new components:
1. Follow the Design System principles
2. Include accessibility features
3. Add comprehensive documentation
4. Provide usage examples
5. Test across all device sizes
