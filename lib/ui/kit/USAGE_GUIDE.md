# 📖 BanaChef AI UI Kit Usage Guide

Comprehensive guide for using the BanaChef UI Kit components effectively.

## 🚀 Quick Start

```dart
import 'package:banachef/ui/kit/ui_kit.dart';

// Now you have access to all components and design tokens
```

## 🎨 Design Tokens

### Colors

```dart
// Primary colors
Container(color: BanaColors.primary)     // Banana Yellow
Container(color: BanaColors.text)        // Deep Charcoal
Container(color: BanaColors.surface)     // Pure White

// System colors
Container(color: BanaColors.success)     // Success Green
Container(color: BanaColors.error)       // Error Red
Container(color: BanaColors.warning)     // Warning Yellow

// Helper methods
Color textColor = BanaColors.getTextColorForBackground(backgroundColor);
bool hasGoodContrast = BanaColors.meetsContrastRequirements(foreground, background);
```

### Typography

```dart
// Display styles
Text('Main Title', style: BanaTypography.title1)
Text('Section Title', style: BanaTypography.title2)
Text('Card Title', style: BanaTypography.title3)

// Body styles
Text('Main content', style: BanaTypography.bodyLarge)
Text('Secondary content', style: BanaTypography.bodyMedium)
Text('Caption text', style: BanaTypography.bodySmall)

// Button styles
Text('Button Text', style: BanaTypography.buttonLarge)

// Helper methods
TextStyle emphasized = BanaTypography.emphasized(BanaTypography.bodyLarge);
TextStyle muted = BanaTypography.muted(BanaTypography.bodyLarge);
TextStyle customColor = BanaTypography.withColor(BanaTypography.headline, Colors.red);
```

### Spacing

```dart
// Direct values
double spacing = BanaSpacing.md;  // 16pt

// EdgeInsets utilities
Padding(
  padding: BanaSpacing.all.lg,           // All directions
  child: Column(
    children: [
      Text('Title'),
      BanaSpacing.verticalSpacing.md,     // Vertical spacing widget
      Text('Content'),
    ],
  ),
)

// Horizontal spacing
Row(
  children: [
    Text('Left'),
    BanaSpacing.horizontalSpacing.sm,
    Text('Right'),
  ],
)
```

### Shadows & Borders

```dart
// Shadows
Container(
  decoration: BoxDecoration(
    boxShadow: BanaShadows.card,         // Card shadow
    borderRadius: BanaBorders.radius.lg, // Large border radius
  ),
)

// Borders
Container(
  decoration: BoxDecoration(
    border: BanaBorders.all.medium,      // Medium border
    borderRadius: BanaBorders.radius.md, // Medium border radius
  ),
)
```

## 🔘 Buttons

### Primary Button
Use for main call-to-action.

```dart
BanaButton.primary(
  text: 'Get Started',
  onPressed: () => navigateToNextScreen(),
  isFullWidth: true,
  icon: Icons.arrow_forward,
)
```

### Secondary Button
Use for secondary actions.

```dart
BanaButton.secondary(
  text: 'Cancel',
  onPressed: () => Navigator.pop(context),
)
```

### Text Button
Use for low-priority actions.

```dart
BanaButton.text(
  text: 'Skip',
  onPressed: () => skipOnboarding(),
)
```

### Icon Button
Use for icon-only actions.

```dart
BanaIconButton(
  icon: Icons.favorite,
  onPressed: () => toggleFavorite(),
  accessibilityLabel: 'Add to favorites',
)

// Variants
BanaIconButton.filled(icon: Icons.share, ...)
BanaIconButton.outlined(icon: Icons.edit, ...)
```

### Floating Action Button
Use for primary creative actions.

```dart
BanaFAB(
  icon: Icons.add,
  onPressed: () => createNewRecipe(),
  accessibilityLabel: 'Create new recipe',
)

// Expandable FAB
BanaFAB.expandable(
  icon: Icons.add,
  accessibilityLabel: 'Add content',
  actions: [
    BanaFABAction(
      icon: Icons.camera_alt,
      label: 'Scan Recipe',
      onPressed: () => scanRecipe(),
    ),
    BanaFABAction(
      icon: Icons.edit,
      label: 'Create Recipe',
      onPressed: () => createRecipe(),
    ),
  ],
)
```

## 🃏 Cards

### Recipe Card
Display recipes with metadata.

```dart
BanaRecipeCard(
  title: 'Pasta Carbonara',
  cookingTime: '30 min',
  difficulty: 'Medium',
  servings: 4,
  rating: 4.5,
  author: 'Chef Mario',
  imageUrl: 'https://example.com/pasta.jpg',
  isFavorite: false,
  onTap: () => openRecipe(),
  onFavoriteToggle: (isFavorite) => updateFavorite(isFavorite),
)

// Variants
BanaRecipeCard(..., variant: BanaRecipeCardVariant.compact)
BanaRecipeCard(..., variant: BanaRecipeCardVariant.featured)
```

### Category Card
Display food categories.

```dart
BanaCategoryCard(
  title: 'Italian Cuisine',
  description: 'Authentic Italian recipes',
  imageUrl: 'https://example.com/italian.jpg',
  recipeCount: 24,
  onTap: () => openCategory(),
)

// Icon-based category
BanaCategoryCard.icon(
  title: 'Desserts',
  icon: Icons.cake,
  backgroundColor: Colors.pink.shade50,
  iconColor: Colors.pink,
  onTap: () => openDesserts(),
)
```

### Pantry Card
Display pantry items with expiration tracking.

```dart
BanaPantryCard(
  name: 'Tomatoes',
  quantity: '2 kg',
  category: 'Vegetables',
  expirationDate: DateTime.now().add(Duration(days: 3)),
  imageUrl: 'https://example.com/tomato.jpg',
  onTap: () => editItem(),
  onEdit: () => showEditDialog(),
  onDelete: () => deleteItem(),
)
```

## 📝 Inputs

### Text Field
Standard text input with validation.

```dart
BanaTextField(
  label: 'Recipe Name',
  hint: 'Enter recipe name',
  required: true,
  validator: (value) => value?.isEmpty == true ? 'Required' : null,
  onChanged: (value) => updateRecipeName(value),
)

// Variants
BanaTextField.password(label: 'Password', ...)
BanaTextField.multiline(label: 'Instructions', maxLines: 5, ...)
```

### Search Bar
Search input with suggestions.

```dart
BanaSearchBar(
  hint: 'Search recipes...',
  onChanged: (query) => performSearch(query),
  suggestions: ['Pasta', 'Pizza', 'Salad'],
  onSuggestionTap: (suggestion) => selectSuggestion(suggestion),
)

// With filter
BanaSearchBar.withFilter(
  hint: 'Search ingredients...',
  onFilterTap: () => showFilterDialog(),
)
```

### Segmented Control
Mutually exclusive options.

```dart
BanaSegmentedControl<String>(
  segments: {
    'week': 'Week',
    'month': 'Month',
  },
  selectedValue: selectedPeriod,
  onSelectionChanged: (value) => setState(() => selectedPeriod = value),
)

// With icons
BanaSegmentedControl<ViewMode>(
  segments: {
    ViewMode.list: BanaSegment(text: 'List', icon: Icons.list),
    ViewMode.grid: BanaSegment(text: 'Grid', icon: Icons.grid_view),
  },
  selectedValue: viewMode,
  onSelectionChanged: (value) => setState(() => viewMode = value),
)
```

### Selection Controls

```dart
// Checkbox
BanaCheckbox(
  value: isSelected,
  onChanged: (value) => setState(() => isSelected = value),
  label: 'Include vegetarian options',
)

// Radio button
BanaRadio<String>(
  value: 'easy',
  groupValue: selectedDifficulty,
  onChanged: (value) => setState(() => selectedDifficulty = value),
  label: 'Easy',
)

// Toggle switch
BanaToggle(
  value: notificationsEnabled,
  onChanged: (value) => setState(() => notificationsEnabled = value),
  label: 'Enable notifications',
)
```

## 🎭 Overlays

### Modal
Critical information and confirmations.

```dart
// Alert modal
BanaModal.show(
  context: context,
  child: BanaModal.alert(
    title: 'Delete Recipe',
    contentText: 'Are you sure you want to delete this recipe?',
    primaryAction: 'Delete',
    onPrimaryAction: () => deleteRecipe(),
    secondaryAction: 'Cancel',
    onSecondaryAction: () => Navigator.pop(context),
  ),
)
```

### Bottom Sheet
Contextual actions and options.

```dart
// Simple bottom sheet
BanaBottomSheet.show(
  context: context,
  title: 'Recipe Options',
  child: RecipeOptionsContent(),
)

// Action sheet
BanaBottomSheet.actionSheet(
  context: context,
  title: 'Share Recipe',
  actions: [
    BanaBottomSheetAction(
      icon: Icons.share,
      title: 'Share Link',
      onTap: () => shareLink(),
    ),
    BanaBottomSheetAction(
      icon: Icons.copy,
      title: 'Copy Link',
      onTap: () => copyLink(),
    ),
  ],
)
```

### Toast
Quick feedback messages.

```dart
// Success toast
BanaToast.success(
  context: context,
  message: 'Recipe saved successfully!',
)

// Error toast
BanaToast.error(
  context: context,
  message: 'Failed to save recipe',
)

// Custom toast
BanaToast.show(
  context: context,
  message: 'Custom message',
  icon: Icons.info,
  backgroundColor: Colors.blue,
)
```

## 🎯 Icons

```dart
// Standard icon
BanaIcon(
  Icons.favorite,
  size: BanaIconSize.medium,
  color: BanaColors.primary,
)

// Semantic icons
BanaIcon.navigation(BanaIcons.home)
BanaIcon.action(BanaIcons.add)
BanaIcon.status(BanaIcons.success, color: BanaColors.success)

// BanaChef specific icons
Icon(BanaIcons.recipe)
Icon(BanaIcons.cookingTime)
Icon(BanaIcons.chef)
```

## ♿ Accessibility

All components include accessibility features:

- **VoiceOver support**: Proper semantic labels
- **Dynamic Type**: Supports iOS text scaling
- **Touch targets**: Minimum 44pt touch areas
- **Color contrast**: WCAG AA compliant
- **Reduce Motion**: Respects motion preferences

```dart
// Always provide accessibility labels for interactive elements
BanaIconButton(
  icon: Icons.favorite,
  onPressed: () => toggleFavorite(),
  accessibilityLabel: 'Add to favorites', // Required!
)
```

## 📱 Responsive Design

Components automatically adapt to different screen sizes:

```dart
// Components work seamlessly with responsive system
ResponsiveLayoutBuilder(
  mobile: (context) => BanaRecipeCard(..., variant: BanaRecipeCardVariant.compact),
  tablet: (context) => BanaRecipeCard(..., variant: BanaRecipeCardVariant.standard),
  desktop: (context) => BanaRecipeCard(..., variant: BanaRecipeCardVariant.featured),
)
```

## 🎨 Customization

Most components support customization while maintaining design consistency:

```dart
BanaButton.primary(
  text: 'Custom Button',
  backgroundColor: Colors.purple,  // Custom color
  borderRadius: 20,               // Custom radius
  onPressed: () {},
)
```

## 🧪 Testing

Components are designed to be easily testable:

```dart
testWidgets('should show success toast', (tester) async {
  await tester.pumpWidget(MyApp());
  
  // Trigger toast
  await tester.tap(find.byType(BanaButton));
  await tester.pump();
  
  // Verify toast appears
  expect(find.text('Success!'), findsOneWidget);
});
```
