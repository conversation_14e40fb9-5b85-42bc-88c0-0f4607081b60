import '../ui_kit.dart';

/// BanaChef AI UI Kit Showcase
/// 
/// Comprehensive showcase of all UI Kit components.
/// Use this as a reference for component usage and styling.
class UIKitShowcase extends StatefulWidget {
  const UIKitShowcase({super.key});

  @override
  State<UIKitShowcase> createState() => _UIKitShowcaseState();
}

class _UIKitShowcaseState extends State<UIKitShowcase> {
  String selectedSegment = 'buttons';
  bool checkboxValue = false;
  String radioValue = 'option1';
  bool toggleValue = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BanaChef UI Kit'),
        backgroundColor: BanaColors.surface,
        foregroundColor: BanaColors.text,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: BanaSpacing.all.lg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Design Tokens'),
            _buildDesignTokensSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Buttons'),
            _buildButtonsSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Cards'),
            _buildCardsSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Inputs'),
            _buildInputsSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Selection Controls'),
            _buildSelectionControlsSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Icons'),
            _buildIconsSection(),
            
            BanaSpacing.verticalSpacing.xl,
            
            _buildSectionTitle('Overlays'),
            _buildOverlaysSection(),
          ],
        ),
      ),
      floatingActionButton: BanaFAB(
        icon: Icons.add,
        accessibilityLabel: 'Add new item',
        onPressed: () => _showToast('FAB pressed!'),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: BanaTypography.title2,
    );
  }

  Widget _buildDesignTokensSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        // Colors
        Text('Colors', style: BanaTypography.headline),
        BanaSpacing.verticalSpacing.sm,
        Wrap(
          spacing: BanaSpacing.sm,
          runSpacing: BanaSpacing.sm,
          children: [
            _buildColorSwatch('Primary', BanaColors.primary),
            _buildColorSwatch('Success', BanaColors.success),
            _buildColorSwatch('Error', BanaColors.error),
            _buildColorSwatch('Warning', BanaColors.warning),
            _buildColorSwatch('Info', BanaColors.info),
          ],
        ),
        
        BanaSpacing.verticalSpacing.lg,
        
        // Typography
        Text('Typography', style: BanaTypography.headline),
        BanaSpacing.verticalSpacing.sm,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Title 1', style: BanaTypography.title1),
            Text('Title 2', style: BanaTypography.title2),
            Text('Title 3', style: BanaTypography.title3),
            Text('Headline', style: BanaTypography.headline),
            Text('Body Large', style: BanaTypography.bodyLarge),
            Text('Body Medium', style: BanaTypography.bodyMedium),
            Text('Body Small', style: BanaTypography.bodySmall),
            Text('Caption', style: BanaTypography.caption),
          ],
        ),
      ],
    );
  }

  Widget _buildColorSwatch(String name, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BanaBorders.radius.sm,
            border: BanaBorders.all.thin,
          ),
        ),
        BanaSpacing.verticalSpacing.xs,
        Text(name, style: BanaTypography.caption),
      ],
    );
  }

  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        // Primary buttons
        Row(
          children: [
            Expanded(
              child: BanaButton.primary(
                text: 'Primary',
                onPressed: () => _showToast('Primary button pressed'),
              ),
            ),
            BanaSpacing.horizontalSpacing.md,
            Expanded(
              child: BanaButton.primary(
                text: 'With Icon',
                icon: Icons.star,
                onPressed: () => _showToast('Primary with icon pressed'),
              ),
            ),
          ],
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Secondary buttons
        Row(
          children: [
            Expanded(
              child: BanaButton.secondary(
                text: 'Secondary',
                onPressed: () => _showToast('Secondary button pressed'),
              ),
            ),
            BanaSpacing.horizontalSpacing.md,
            Expanded(
              child: BanaButton.text(
                text: 'Text Button',
                onPressed: () => _showToast('Text button pressed'),
              ),
            ),
          ],
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Icon buttons
        Row(
          children: [
            BanaIconButton(
              icon: Icons.favorite,
              onPressed: () => _showToast('Favorite pressed'),
              accessibilityLabel: 'Add to favorites',
            ),
            BanaSpacing.horizontalSpacing.md,
            BanaIconButton.filled(
              icon: Icons.share,
              onPressed: () => _showToast('Share pressed'),
              accessibilityLabel: 'Share',
            ),
            BanaSpacing.horizontalSpacing.md,
            BanaIconButton.outlined(
              icon: Icons.edit,
              onPressed: () => _showToast('Edit pressed'),
              accessibilityLabel: 'Edit',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        // Recipe card
        BanaRecipeCard(
          title: 'Delicious Pasta Carbonara',
          cookingTime: '30 min',
          difficulty: 'Medium',
          servings: 4,
          rating: 4.5,
          author: 'Chef Mario',
          isFavorite: false,
          onTap: () => _showToast('Recipe card tapped'),
          onFavoriteToggle: (isFavorite) => _showToast('Favorite toggled: $isFavorite'),
        ),
        
        BanaSpacing.verticalSpacing.lg,
        
        // Category card
        BanaCategoryCard.icon(
          title: 'Italian Cuisine',
          description: 'Authentic Italian recipes',
          icon: Icons.local_pizza,
          recipeCount: 24,
          onTap: () => _showToast('Category card tapped'),
        ),
      ],
    );
  }

  Widget _buildInputsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        // Text field
        const BanaTextField(
          label: 'Recipe Name',
          hint: 'Enter recipe name',
          required: true,
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Search bar
        BanaSearchBar(
          hint: 'Search recipes...',
          onChanged: (query) => debugPrint('Search: $query'),
          suggestions: const ['Pasta', 'Pizza', 'Salad', 'Soup'],
          onSuggestionTap: (suggestion) => _showToast('Selected: $suggestion'),
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Segmented control
        BanaSegmentedControl<String>(
          segments: const {
            'week': 'Week',
            'month': 'Month',
          },
          selectedValue: selectedSegment,
          onSelectionChanged: (value) {
            setState(() => selectedSegment = value);
            _showToast('Selected: $value');
          },
        ),
      ],
    );
  }

  Widget _buildSelectionControlsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        // Checkbox
        BanaCheckbox(
          value: checkboxValue,
          onChanged: (value) => setState(() => checkboxValue = value),
          label: 'Include vegetarian options',
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Radio buttons
        Column(
          children: [
            BanaRadio<String>(
              value: 'option1',
              groupValue: radioValue,
              onChanged: (value) => setState(() => radioValue = value!),
              label: 'Option 1',
            ),
            BanaRadio<String>(
              value: 'option2',
              groupValue: radioValue,
              onChanged: (value) => setState(() => radioValue = value!),
              label: 'Option 2',
            ),
          ],
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Toggle
        BanaToggle(
          value: toggleValue,
          onChanged: (value) => setState(() => toggleValue = value),
          label: 'Enable notifications',
        ),
      ],
    );
  }

  Widget _buildIconsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        Wrap(
          spacing: BanaSpacing.lg,
          runSpacing: BanaSpacing.lg,
          children: [
            BanaIcon.navigation(BanaIcons.home),
            BanaIcon.action(BanaIcons.add),
            BanaIcon.status(BanaIcons.success, color: BanaColors.success),
            BanaIcon.info(BanaIcons.info, color: BanaColors.info),
            BanaIcon(BanaIcons.chef, size: BanaIconSize.large),
          ],
        ),
      ],
    );
  }

  Widget _buildOverlaysSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BanaSpacing.verticalSpacing.md,
        
        Row(
          children: [
            Expanded(
              child: BanaButton.secondary(
                text: 'Show Modal',
                onPressed: _showModal,
              ),
            ),
            BanaSpacing.horizontalSpacing.md,
            Expanded(
              child: BanaButton.secondary(
                text: 'Show Bottom Sheet',
                onPressed: _showBottomSheet,
              ),
            ),
          ],
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        Row(
          children: [
            Expanded(
              child: BanaButton.secondary(
                text: 'Success Toast',
                onPressed: () => BanaToast.success(
                  context: context,
                  message: 'Operation completed successfully!',
                ),
              ),
            ),
            BanaSpacing.horizontalSpacing.md,
            Expanded(
              child: BanaButton.secondary(
                text: 'Error Toast',
                onPressed: () => BanaToast.error(
                  context: context,
                  message: 'Something went wrong!',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showToast(String message) {
    BanaToast.info(
      context: context,
      message: message,
    );
  }

  void _showModal() {
    BanaModal.show(
      context: context,
      child: const BanaModal.confirmation(
        title: 'Delete Recipe',
        contentText: 'Are you sure you want to delete this recipe? This action cannot be undone.',
        primaryAction: 'Delete',
        secondaryAction: 'Cancel',
        onPrimaryAction: null,
        onSecondaryAction: null,
      ),
    );
  }

  void _showBottomSheet() {
    BanaBottomSheet.actionSheet(
      context: context,
      title: 'Recipe Options',
      actions: [
        BanaBottomSheetAction(
          icon: Icons.edit,
          title: 'Edit Recipe',
          onTap: () => _showToast('Edit selected'),
        ),
        BanaBottomSheetAction(
          icon: Icons.share,
          title: 'Share Recipe',
          onTap: () => _showToast('Share selected'),
        ),
        BanaBottomSheetAction(
          icon: Icons.delete,
          title: 'Delete Recipe',
          isDestructive: true,
          onTap: () => _showToast('Delete selected'),
        ),
      ],
    );
  }
}
