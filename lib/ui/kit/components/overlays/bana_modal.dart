import 'package:flutter/material.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_button.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Modal Component
/// 
/// Modal dialog component following the BanaChef Design System.
/// Used for critical information, confirmations, and blocking interactions.
/// 
/// Usage:
/// ```dart
/// // Simple alert modal
/// BanaModal.alert(
///   title: 'Delete Recipe',
///   content: 'Are you sure you want to delete this recipe?',
///   primaryAction: 'Delete',
///   onPrimaryAction: () => deleteRecipe(),
///   secondaryAction: 'Cancel',
///   onSecondaryAction: () => Navigator.pop(context),
/// )
/// 
/// // Custom modal
/// BanaModal.show(
///   context: context,
///   child: CustomModalContent(),
/// )
/// ```
class BanaModal extends StatefulWidget {
  /// Modal title
  final String? title;
  
  /// Modal content widget
  final Widget? content;
  
  /// Modal content text (alternative to content widget)
  final String? contentText;
  
  /// Primary action button text
  final String? primaryAction;
  
  /// Secondary action button text
  final String? secondaryAction;
  
  /// Callback for primary action
  final VoidCallback? onPrimaryAction;
  
  /// Callback for secondary action
  final VoidCallback? onSecondaryAction;
  
  /// Whether modal can be dismissed by tapping outside
  final bool dismissible;
  
  /// Whether to show close button
  final bool showCloseButton;
  
  /// Custom width for modal
  final double? width;
  
  /// Custom height for modal
  final double? height;
  
  /// Modal variant
  final BanaModalVariant variant;

  const BanaModal({
    super.key,
    this.title,
    this.content,
    this.contentText,
    this.primaryAction,
    this.secondaryAction,
    this.onPrimaryAction,
    this.onSecondaryAction,
    this.dismissible = true,
    this.showCloseButton = true,
    this.width,
    this.height,
    this.variant = BanaModalVariant.standard,
  }) : assert(content != null || contentText != null, 'Either content or contentText must be provided');

  /// Alert modal constructor
  const BanaModal.alert({
    super.key,
    required this.title,
    this.content,
    this.contentText,
    required this.primaryAction,
    this.secondaryAction,
    required this.onPrimaryAction,
    this.onSecondaryAction,
    this.dismissible = true,
    this.width,
    this.height,
  }) : showCloseButton = false,
       variant = BanaModalVariant.alert;

  /// Confirmation modal constructor
  const BanaModal.confirmation({
    super.key,
    required this.title,
    this.content,
    this.contentText,
    required this.primaryAction,
    required this.secondaryAction,
    required this.onPrimaryAction,
    required this.onSecondaryAction,
    this.dismissible = true,
    this.width,
    this.height,
  }) : showCloseButton = false,
       variant = BanaModalVariant.confirmation;

  /// Show modal dialog
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    bool dismissible = true,
    Color? barrierColor,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: dismissible,
      barrierColor: barrierColor ?? BanaColors.scrim,
      builder: (context) => child,
    );
  }

  @override
  State<BanaModal> createState() => _BanaModalState();
}

class _BanaModalState extends State<BanaModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onDismiss() {
    if (widget.dismissible) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: _buildModalContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModalContent() {
    return Container(
      width: widget.width,
      height: widget.height,
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.9,
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: BanaColors.surface,
        borderRadius: BanaBorders.radius.xl,
        boxShadow: BanaShadows.modal,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          if (widget.title != null || widget.showCloseButton)
            _buildHeader(),
          
          // Content
          Flexible(
            child: _buildContent(),
          ),
          
          // Actions
          if (widget.primaryAction != null || widget.secondaryAction != null)
            _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: BanaSpacing.all.lg,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: BanaColors.borderLight,
            width: BanaBorders.widthThin,
          ),
        ),
      ),
      child: Row(
        children: [
          if (widget.title != null)
            Expanded(
              child: Text(
                widget.title!,
                style: BanaTypography.title3,
              ),
            ),
          
          if (widget.showCloseButton)
            BanaIconButton(
              icon: Icons.close,
              onPressed: _onDismiss,
              size: BanaIconButtonSize.small,
              accessibilityLabel: 'Close modal',
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    Widget contentWidget;
    
    if (widget.content != null) {
      contentWidget = widget.content!;
    } else if (widget.contentText != null) {
      contentWidget = Text(
        widget.contentText!,
        style: BanaTypography.bodyLarge,
        textAlign: TextAlign.center,
      );
    } else {
      contentWidget = const SizedBox.shrink();
    }
    
    return Container(
      width: double.infinity,
      padding: BanaSpacing.all.lg,
      child: contentWidget,
    );
  }

  Widget _buildActions() {
    final actions = <Widget>[];
    
    if (widget.secondaryAction != null) {
      actions.add(
        Expanded(
          child: BanaButton.secondary(
            text: widget.secondaryAction!,
            onPressed: widget.onSecondaryAction ?? _onDismiss,
          ),
        ),
      );
    }
    
    if (widget.primaryAction != null) {
      if (actions.isNotEmpty) {
        actions.add(BanaSpacing.horizontalSpacing.md);
      }
      
      actions.add(
        Expanded(
          child: BanaButton.primary(
            text: widget.primaryAction!,
            onPressed: widget.onPrimaryAction,
            backgroundColor: widget.variant == BanaModalVariant.alert
                ? BanaColors.error
                : null,
          ),
        ),
      );
    }
    
    return Container(
      padding: BanaSpacing.all.lg,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: BanaColors.borderLight,
            width: BanaBorders.widthThin,
          ),
        ),
      ),
      child: Row(children: actions),
    );
  }
}

/// Modal variant types
enum BanaModalVariant {
  /// Standard modal
  standard,
  
  /// Alert modal (destructive action)
  alert,
  
  /// Confirmation modal
  confirmation,
}
