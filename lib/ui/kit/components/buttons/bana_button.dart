import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Button Component
/// 
/// Comprehensive button component following the BanaChef Design System.
/// Supports Primary, Secondary, and Text button variants with proper states and animations.
/// 
/// Usage:
/// ```dart
/// // Primary button (main CTA)
/// BanaButton.primary(
///   text: 'Get Started',
///   onPressed: () {},
/// )
/// 
/// // Secondary button
/// BanaButton.secondary(
///   text: 'Cancel',
///   onPressed: () {},
/// )
/// 
/// // Text button (low priority)
/// BanaButton.text(
///   text: 'Skip',
///   onPressed: () {},
/// )
/// ```
class BanaButton extends StatefulWidget {
  /// Button text
  final String text;
  
  /// Callback when button is pressed
  final VoidCallback? onPressed;
  
  /// Button variant type
  final BanaButtonType type;
  
  /// Button size
  final BanaButtonSize size;
  
  /// Whether button is in loading state
  final bool isLoading;
  
  /// Whether button should take full width
  final bool isFullWidth;
  
  /// Optional icon to display before text
  final IconData? icon;
  
  /// Custom background color (overrides theme)
  final Color? backgroundColor;
  
  /// Custom text color (overrides theme)
  final Color? textColor;
  
  /// Custom border radius (overrides theme)
  final double? borderRadius;

  const BanaButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = BanaButtonType.primary,
    this.size = BanaButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  });

  /// Primary button constructor
  const BanaButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = BanaButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  }) : type = BanaButtonType.primary;

  /// Secondary button constructor
  const BanaButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = BanaButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  }) : type = BanaButtonType.secondary;

  /// Text button constructor
  const BanaButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = BanaButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  }) : type = BanaButtonType.text;

  @override
  State<BanaButton> createState() => _BanaButtonState();
}

class _BanaButtonState extends State<BanaButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: isEnabled ? widget.onPressed : null,
            child: Container(
              width: widget.isFullWidth ? double.infinity : null,
              height: _getButtonHeight(),
              decoration: _getButtonDecoration(isEnabled),
              child: _buildButtonContent(),
            ),
          ),
        );
      },
    );
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case BanaButtonSize.small:
        return 36.0;
      case BanaButtonSize.medium:
        return 48.0;
      case BanaButtonSize.large:
        return 56.0;
    }
  }

  BoxDecoration _getButtonDecoration(bool isEnabled) {
    Color backgroundColor;
    List<BoxShadow> boxShadow;
    Border? border;

    switch (widget.type) {
      case BanaButtonType.primary:
        backgroundColor = widget.backgroundColor ?? 
            (isEnabled ? BanaColors.primary : BanaColors.disabled);
        boxShadow = isEnabled ? 
            (_isPressed ? BanaShadows.buttonPressed : BanaShadows.button) : 
            BanaShadows.none;
        break;
      case BanaButtonType.secondary:
        backgroundColor = widget.backgroundColor ?? BanaColors.surface;
        border = Border.all(
          color: isEnabled ? BanaColors.border : BanaColors.disabled,
          width: BanaBorders.widthMedium,
        );
        boxShadow = isEnabled ? 
            (_isPressed ? BanaShadows.buttonPressed : BanaShadows.button) : 
            BanaShadows.none;
        break;
      case BanaButtonType.text:
        backgroundColor = widget.backgroundColor ?? Colors.transparent;
        boxShadow = BanaShadows.none;
        break;
    }

    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(
        widget.borderRadius ?? BanaBorders.buttonRadius,
      ),
      boxShadow: boxShadow,
      border: border,
    );
  }

  Widget _buildButtonContent() {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    if (widget.isLoading) {
      return Center(
        child: SizedBox(
          width: _getLoadingIndicatorSize(),
          height: _getLoadingIndicatorSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getLoadingIndicatorColor(),
            ),
          ),
        ),
      );
    }

    final textStyle = _getTextStyle(isEnabled);
    final horizontalPadding = _getHorizontalPadding();

    Widget content = Text(
      widget.text,
      style: textStyle,
      textAlign: TextAlign.center,
    );

    if (widget.icon != null) {
      content = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.icon,
            size: _getIconSize(),
            color: textStyle.color,
          ),
          BanaSpacing.horizontalSpacing.sm,
          Flexible(child: content),
        ],
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Center(child: content),
    );
  }

  TextStyle _getTextStyle(bool isEnabled) {
    TextStyle baseStyle;
    Color textColor;

    switch (widget.size) {
      case BanaButtonSize.small:
        baseStyle = BanaTypography.buttonSmall;
        break;
      case BanaButtonSize.medium:
        baseStyle = BanaTypography.buttonMedium;
        break;
      case BanaButtonSize.large:
        baseStyle = BanaTypography.buttonLarge;
        break;
    }

    switch (widget.type) {
      case BanaButtonType.primary:
        textColor = widget.textColor ?? 
            (isEnabled ? BanaColors.onPrimary : BanaColors.onDisabled);
        break;
      case BanaButtonType.secondary:
      case BanaButtonType.text:
        textColor = widget.textColor ?? 
            (isEnabled ? BanaColors.text : BanaColors.onDisabled);
        break;
    }

    return baseStyle.copyWith(color: textColor);
  }

  double _getHorizontalPadding() {
    switch (widget.size) {
      case BanaButtonSize.small:
        return BanaSpacing.md;
      case BanaButtonSize.medium:
        return BanaSpacing.lg;
      case BanaButtonSize.large:
        return BanaSpacing.xl;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case BanaButtonSize.small:
        return 16.0;
      case BanaButtonSize.medium:
        return 20.0;
      case BanaButtonSize.large:
        return 24.0;
    }
  }

  double _getLoadingIndicatorSize() {
    switch (widget.size) {
      case BanaButtonSize.small:
        return 16.0;
      case BanaButtonSize.medium:
        return 20.0;
      case BanaButtonSize.large:
        return 24.0;
    }
  }

  Color _getLoadingIndicatorColor() {
    switch (widget.type) {
      case BanaButtonType.primary:
        return BanaColors.onPrimary;
      case BanaButtonType.secondary:
      case BanaButtonType.text:
        return BanaColors.text;
    }
  }
}

/// Button type variants
enum BanaButtonType {
  /// Primary button - Main call-to-action
  primary,
  
  /// Secondary button - Secondary actions
  secondary,
  
  /// Text button - Low-priority actions
  text,
}

/// Button size variants
enum BanaButtonSize {
  /// Small button - 36pt height
  small,
  
  /// Medium button - 48pt height
  medium,
  
  /// Large button - 56pt height
  large,
}
