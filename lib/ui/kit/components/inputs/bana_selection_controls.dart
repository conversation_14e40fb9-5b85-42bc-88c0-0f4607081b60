import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Selection Controls Components
/// 
/// Collection of selection control components following the BanaChef Design System.
/// Includes checkboxes, radio buttons, and toggle switches.
/// 
/// Usage:
/// ```dart
/// // Checkbox
/// BanaCheckbox(
///   value: isSelected,
///   onChanged: (value) => setState(() => isSelected = value),
///   label: 'Include vegetarian options',
/// )
/// 
/// // Radio button
/// BanaRadio<String>(
///   value: 'easy',
///   groupValue: selectedDifficulty,
///   onChanged: (value) => setState(() => selectedDifficulty = value),
///   label: 'Easy',
/// )
/// 
/// // Toggle switch
/// BanaToggle(
///   value: notificationsEnabled,
///   onChanged: (value) => setState(() => notificationsEnabled = value),
///   label: 'Enable notifications',
/// )
/// ```

// ============================================================================
// CHECKBOX COMPONENT
// ============================================================================

class BanaCheckbox extends StatelessWidget {
  /// Whether checkbox is checked
  final bool value;
  
  /// Callback when checkbox state changes
  final ValueChanged<bool>? onChanged;
  
  /// Checkbox label
  final String? label;
  
  /// Label widget (alternative to text label)
  final Widget? labelWidget;
  
  /// Whether checkbox is enabled
  final bool enabled;
  
  /// Custom active color
  final Color? activeColor;
  
  /// Custom check color
  final Color? checkColor;
  
  /// Label position relative to checkbox
  final BanaLabelPosition labelPosition;

  const BanaCheckbox({
    super.key,
    required this.value,
    this.onChanged,
    this.label,
    this.labelWidget,
    this.enabled = true,
    this.activeColor,
    this.checkColor,
    this.labelPosition = BanaLabelPosition.right,
  }) : assert(label != null || labelWidget != null, 'Either label or labelWidget must be provided');

  @override
  Widget build(BuildContext context) {
    final isEnabled = enabled && onChanged != null;
    
    Widget checkbox = SizedBox(
      width: 24,
      height: 24,
      child: Checkbox(
        value: value,
        onChanged: isEnabled ? (bool? newValue) => onChanged?.call(newValue ?? false) : null,
        activeColor: activeColor ?? BanaColors.primary,
        checkColor: checkColor ?? BanaColors.onPrimary,
        side: BorderSide(
          color: isEnabled ? BanaColors.border : BanaColors.disabled,
          width: BanaBorders.widthMedium,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BanaBorders.radius.xs,
        ),
      ),
    );
    
    Widget labelContent = labelWidget ?? Text(
      label!,
      style: BanaTypography.bodyMedium.copyWith(
        color: isEnabled ? BanaColors.text : BanaColors.onDisabled,
      ),
    );
    
    return GestureDetector(
      onTap: isEnabled ? () => onChanged!(!value) : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: labelPosition == BanaLabelPosition.left
            ? [
                labelContent,
                BanaSpacing.horizontalSpacing.sm,
                checkbox,
              ]
            : [
                checkbox,
                BanaSpacing.horizontalSpacing.sm,
                Flexible(child: labelContent),
              ],
      ),
    );
  }
}

// ============================================================================
// RADIO BUTTON COMPONENT
// ============================================================================

class BanaRadio<T> extends StatelessWidget {
  /// Radio button value
  final T value;
  
  /// Currently selected value in the group
  final T? groupValue;
  
  /// Callback when radio button is selected
  final ValueChanged<T?>? onChanged;
  
  /// Radio button label
  final String? label;
  
  /// Label widget (alternative to text label)
  final Widget? labelWidget;
  
  /// Whether radio button is enabled
  final bool enabled;
  
  /// Custom active color
  final Color? activeColor;
  
  /// Label position relative to radio button
  final BanaLabelPosition labelPosition;

  const BanaRadio({
    super.key,
    required this.value,
    required this.groupValue,
    this.onChanged,
    this.label,
    this.labelWidget,
    this.enabled = true,
    this.activeColor,
    this.labelPosition = BanaLabelPosition.right,
  }) : assert(label != null || labelWidget != null, 'Either label or labelWidget must be provided');

  @override
  Widget build(BuildContext context) {
    final isEnabled = enabled && onChanged != null;
    final isSelected = value == groupValue;
    
    Widget radio = SizedBox(
      width: 24,
      height: 24,
      child: Radio<T>(
        value: value,
        groupValue: groupValue,
        onChanged: isEnabled ? onChanged : null,
        activeColor: activeColor ?? BanaColors.primary,
      ),
    );
    
    Widget labelContent = labelWidget ?? Text(
      label!,
      style: BanaTypography.bodyMedium.copyWith(
        color: isEnabled ? BanaColors.text : BanaColors.onDisabled,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
    
    return GestureDetector(
      onTap: isEnabled ? () => onChanged!(value) : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: labelPosition == BanaLabelPosition.left
            ? [
                labelContent,
                BanaSpacing.horizontalSpacing.sm,
                radio,
              ]
            : [
                radio,
                BanaSpacing.horizontalSpacing.sm,
                Flexible(child: labelContent),
              ],
      ),
    );
  }
}

// ============================================================================
// TOGGLE SWITCH COMPONENT
// ============================================================================

class BanaToggle extends StatefulWidget {
  /// Whether toggle is on
  final bool value;
  
  /// Callback when toggle state changes
  final ValueChanged<bool>? onChanged;
  
  /// Toggle label
  final String? label;
  
  /// Label widget (alternative to text label)
  final Widget? labelWidget;
  
  /// Whether toggle is enabled
  final bool enabled;
  
  /// Custom active color
  final Color? activeColor;
  
  /// Custom inactive color
  final Color? inactiveColor;
  
  /// Custom thumb color
  final Color? thumbColor;
  
  /// Label position relative to toggle
  final BanaLabelPosition labelPosition;

  const BanaToggle({
    super.key,
    required this.value,
    this.onChanged,
    this.label,
    this.labelWidget,
    this.enabled = true,
    this.activeColor,
    this.inactiveColor,
    this.thumbColor,
    this.labelPosition = BanaLabelPosition.left,
  }) : assert(label != null || labelWidget != null, 'Either label or labelWidget must be provided');

  @override
  State<BanaToggle> createState() => _BanaToggleState();
}

class _BanaToggleState extends State<BanaToggle>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    if (widget.value) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(BanaToggle oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      if (widget.value) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTap() {
    if (widget.enabled && widget.onChanged != null) {
      HapticFeedback.lightImpact();
      widget.onChanged!(!widget.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.enabled && widget.onChanged != null;
    
    Widget toggle = GestureDetector(
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            width: 48,
            height: 28,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Color.lerp(
                widget.inactiveColor ?? BanaColors.border,
                widget.activeColor ?? BanaColors.primary,
                _animation.value,
              ),
            ),
            child: Stack(
              children: [
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  left: widget.value ? 22 : 2,
                  top: 2,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.thumbColor ?? BanaColors.surface,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
    
    Widget labelContent = widget.labelWidget ?? Text(
      widget.label!,
      style: BanaTypography.bodyMedium.copyWith(
        color: isEnabled ? BanaColors.text : BanaColors.onDisabled,
      ),
    );
    
    return GestureDetector(
      onTap: _onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: widget.labelPosition == BanaLabelPosition.left
            ? [
                Flexible(child: labelContent),
                BanaSpacing.horizontalSpacing.md,
                toggle,
              ]
            : [
                toggle,
                BanaSpacing.horizontalSpacing.md,
                Flexible(child: labelContent),
              ],
      ),
    );
  }
}

// ============================================================================
// ENUMS AND UTILITIES
// ============================================================================

/// Label position relative to the control
enum BanaLabelPosition {
  /// Label appears to the left of the control
  left,
  
  /// Label appears to the right of the control
  right,
}
