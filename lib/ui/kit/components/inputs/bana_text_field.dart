import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Text Field Component
/// 
/// Text input component following the BanaChef Design System.
/// Supports various states, validation, and accessibility features.
/// 
/// Usage:
/// ```dart
/// BanaTextField(
///   label: 'Recipe Name',
///   hint: 'Enter recipe name',
///   onChanged: (value) => setState(() => recipeName = value),
///   validator: (value) => value?.isEmpty == true ? 'Required' : null,
/// )
/// 
/// // Password field
/// BanaTextField.password(
///   label: 'Password',
///   hint: 'Enter your password',
/// )
/// 
/// // Multiline field
/// BanaTextField.multiline(
///   label: 'Instructions',
///   hint: 'Enter cooking instructions',
///   maxLines: 5,
/// )
/// ```
class BanaTextField extends StatefulWidget {
  /// Field label
  final String? label;
  
  /// Placeholder text
  final String? hint;
  
  /// Initial value
  final String? initialValue;
  
  /// Text controller
  final TextEditingController? controller;
  
  /// Whether field is enabled
  final bool enabled;
  
  /// Whether field is read-only
  final bool readOnly;
  
  /// Whether field is required
  final bool required;
  
  /// Whether to obscure text (for passwords)
  final bool obscureText;
  
  /// Maximum number of lines
  final int? maxLines;
  
  /// Minimum number of lines
  final int? minLines;
  
  /// Maximum character length
  final int? maxLength;
  
  /// Keyboard type
  final TextInputType keyboardType;
  
  /// Text input action
  final TextInputAction textInputAction;
  
  /// Input formatters
  final List<TextInputFormatter>? inputFormatters;
  
  /// Validation function
  final String? Function(String?)? validator;
  
  /// Callback when text changes
  final ValueChanged<String>? onChanged;
  
  /// Callback when field is submitted
  final ValueChanged<String>? onSubmitted;
  
  /// Callback when field is tapped
  final VoidCallback? onTap;
  
  /// Prefix icon
  final IconData? prefixIcon;
  
  /// Suffix icon
  final IconData? suffixIcon;
  
  /// Suffix icon callback
  final VoidCallback? onSuffixIconTap;
  
  /// Focus node
  final FocusNode? focusNode;
  
  /// Auto focus
  final bool autofocus;
  
  /// Field variant
  final BanaTextFieldVariant variant;

  /// Error message to display
  final String? errorText;

  /// Whether field has error state
  final bool hasError;

  const BanaTextField({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  });

  /// Password text field constructor
  const BanaTextField.password({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  }) : obscureText = true,
       maxLines = 1,
       minLines = null,
       keyboardType = TextInputType.visiblePassword,
       textInputAction = TextInputAction.done,
       suffixIcon = null,
       onSuffixIconTap = null;

  /// Multiline text field constructor
  const BanaTextField.multiline({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.maxLines = 5,
    this.minLines = 3,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.focusNode,
    this.autofocus = false,
    this.variant = BanaTextFieldVariant.outlined,
    this.errorText,
    this.hasError = false,
  }) : obscureText = false,
       keyboardType = TextInputType.multiline,
       textInputAction = TextInputAction.newline;

  @override
  State<BanaTextField> createState() => _BanaTextFieldState();
}

class _BanaTextFieldState extends State<BanaTextField> {
  late FocusNode _focusNode;
  late TextEditingController _controller;
  bool _obscureText = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _obscureText = widget.obscureText;
    
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {});
  }

  void _onChanged(String value) {
    // Clear error when user starts typing
    if (_errorText != null) {
      setState(() => _errorText = null);
    }
    
    widget.onChanged?.call(value);
  }

  void _validate() {
    if (widget.validator != null) {
      final error = widget.validator!(_controller.text);
      setState(() => _errorText = error);
    }
  }

  void _toggleObscureText() {
    setState(() => _obscureText = !_obscureText);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label != null)
          _buildLabel(),
        
        if (widget.label != null) BanaSpacing.verticalSpacing.sm,
        
        // Text field
        _buildTextField(),
        
        // Error text
        if (_errorText != null || widget.errorText != null) ...[
          BanaSpacing.verticalSpacing.xs,
          _buildErrorText(),
        ],
      ],
    );
  }

  Widget _buildLabel() {
    return RichText(
      text: TextSpan(
        text: widget.label!,
        style: BanaTypography.labelLarge.copyWith(
          color: widget.enabled ? BanaColors.text : BanaColors.onDisabled,
        ),
        children: [
          if (widget.required)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: BanaColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField() {
    final isFocused = _focusNode.hasFocus;
    final hasError = _errorText != null;
    
    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      obscureText: _obscureText,
      autofocus: widget.autofocus,
      maxLines: widget.obscureText ? 1 : widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      onChanged: _onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      onEditingComplete: _validate,
      style: BanaTypography.bodyLarge.copyWith(
        color: widget.enabled ? BanaColors.text : BanaColors.onDisabled,
      ),
      decoration: _buildInputDecoration(isFocused, widget.hasError || hasError),
    );
  }

  InputDecoration _buildInputDecoration(bool isFocused, bool hasError) {
    Color borderColor;
    double borderWidth;
    
    if (hasError) {
      borderColor = BanaColors.error;
      borderWidth = BanaBorders.widthThick;
    } else if (isFocused) {
      borderColor = BanaColors.primary;
      borderWidth = BanaBorders.widthThick;
    } else {
      borderColor = widget.enabled ? BanaColors.border : BanaColors.disabled;
      borderWidth = BanaBorders.widthThin;
    }

    return InputDecoration(
      hintText: widget.hint,
      hintStyle: BanaTypography.bodyLarge.copyWith(
        color: BanaColors.textTertiary,
      ),
      prefixIcon: widget.prefixIcon != null
          ? Icon(
              widget.prefixIcon,
              color: isFocused ? BanaColors.primary : BanaColors.textSecondary,
              size: 20,
            )
          : null,
      suffixIcon: _buildSuffixIcon(isFocused),
      filled: true,
      fillColor: widget.enabled ? BanaColors.surface : BanaColors.disabled,
      border: _buildBorder(borderColor, borderWidth),
      enabledBorder: _buildBorder(borderColor, borderWidth),
      focusedBorder: _buildBorder(borderColor, borderWidth),
      errorBorder: _buildBorder(BanaColors.error, BanaBorders.widthThick),
      focusedErrorBorder: _buildBorder(BanaColors.error, BanaBorders.widthThick),
      disabledBorder: _buildBorder(BanaColors.disabled, BanaBorders.widthThin),
      contentPadding: EdgeInsets.symmetric(
        horizontal: BanaSpacing.md,
        vertical: widget.maxLines == 1 ? BanaSpacing.md : BanaSpacing.lg,
      ),
      counterText: '', // Hide character counter
      errorText: widget.errorText, // Display error text if provided
    );
  }

  Widget? _buildSuffixIcon(bool isFocused) {
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility : Icons.visibility_off,
          color: isFocused ? BanaColors.primary : BanaColors.textSecondary,
          size: 20,
        ),
        onPressed: _toggleObscureText,
      );
    }
    
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          color: isFocused ? BanaColors.primary : BanaColors.textSecondary,
          size: 20,
        ),
        onPressed: widget.onSuffixIconTap,
      );
    }
    
    return null;
  }

  OutlineInputBorder _buildBorder(Color color, double width) {
    switch (widget.variant) {
      case BanaTextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BanaBorders.radius.md,
          borderSide: BorderSide(color: color, width: width),
        );
      case BanaTextFieldVariant.rounded:
        return OutlineInputBorder(
          borderRadius: BanaBorders.radius.circular,
          borderSide: BorderSide(color: color, width: width),
        );
    }
  }

  Widget _buildErrorText() {
    final errorMessage = widget.errorText ?? _errorText;
    return Text(
      errorMessage!,
      style: BanaTypography.caption.copyWith(
        color: BanaColors.error,
      ),
    );
  }
}

/// Text field variant types
enum BanaTextFieldVariant {
  /// Standard outlined text field
  outlined,
  
  /// Rounded text field with circular borders
  rounded,
}
