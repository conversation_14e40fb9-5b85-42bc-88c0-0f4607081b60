import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_borders.dart';
import '../../tokens/bana_shadows.dart';

/// BanaChef AI Segmented Control Component
/// 
/// Segmented control component following the BanaChef Design System.
/// Allows users to select between mutually exclusive options.
/// 
/// Usage:
/// ```dart
/// BanaSegmentedControl<String>(
///   segments: {
///     'week': 'Week',
///     'month': 'Month',
///   },
///   selectedValue: selectedPeriod,
///   onSelectionChanged: (value) => setState(() => selectedPeriod = value),
/// )
/// 
/// // With icons
/// BanaSegmentedControl<ViewMode>(
///   segments: {
///     ViewMode.list: BanaSegment(text: 'List', icon: Icons.list),
///     ViewMode.grid: BanaSegment(text: 'Grid', icon: Icons.grid_view),
///   },
///   selectedValue: viewMode,
///   onSelectionChanged: (value) => setState(() => viewMode = value),
/// )
/// ```
class BanaSegmentedControl<T> extends StatefulWidget {
  /// Map of segment values to their display content
  final Map<T, dynamic> segments;
  
  /// Currently selected value
  final T selectedValue;
  
  /// Callback when selection changes
  final ValueChanged<T> onSelectionChanged;
  
  /// Whether the control is enabled
  final bool enabled;
  
  /// Control variant
  final BanaSegmentedControlVariant variant;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Custom selected color
  final Color? selectedColor;
  
  /// Custom text color
  final Color? textColor;
  
  /// Custom selected text color
  final Color? selectedTextColor;

  const BanaSegmentedControl({
    super.key,
    required this.segments,
    required this.selectedValue,
    required this.onSelectionChanged,
    this.enabled = true,
    this.variant = BanaSegmentedControlVariant.filled,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
    this.selectedTextColor,
  });

  @override
  State<BanaSegmentedControl<T>> createState() => _BanaSegmentedControlState<T>();
}

class _BanaSegmentedControlState<T> extends State<BanaSegmentedControl<T>>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(BanaSegmentedControl<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedValue != widget.selectedValue) {
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? _getBackgroundColor(),
        borderRadius: BanaBorders.radius.md,
        border: widget.variant == BanaSegmentedControlVariant.outlined
            ? Border.all(color: BanaColors.border)
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: widget.segments.entries.map((entry) {
          final value = entry.key;
          final content = entry.value;
          final isSelected = value == widget.selectedValue;
          final isFirst = widget.segments.keys.first == value;
          final isLast = widget.segments.keys.last == value;
          
          return _buildSegment(
            value: value,
            content: content,
            isSelected: isSelected,
            isFirst: isFirst,
            isLast: isLast,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSegment({
    required T value,
    required dynamic content,
    required bool isSelected,
    required bool isFirst,
    required bool isLast,
  }) {
    return Expanded(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return GestureDetector(
            onTap: widget.enabled ? () => _onSegmentTap(value) : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              margin: EdgeInsets.all(BanaSpacing.xs / 2),
              padding: EdgeInsets.symmetric(
                horizontal: BanaSpacing.md,
                vertical: BanaSpacing.sm,
              ),
              decoration: BoxDecoration(
                color: isSelected ? _getSelectedColor() : Colors.transparent,
                borderRadius: BorderRadius.circular(BanaBorders.radiusSM),
                boxShadow: isSelected && widget.variant == BanaSegmentedControlVariant.filled
                    ? BanaShadows.elevation1
                    : null,
              ),
              child: _buildSegmentContent(content, isSelected),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSegmentContent(dynamic content, bool isSelected) {
    if (content is String) {
      return _buildTextSegment(content, isSelected);
    } else if (content is BanaSegment) {
      return _buildCustomSegment(content, isSelected);
    } else {
      return _buildTextSegment(content.toString(), isSelected);
    }
  }

  Widget _buildTextSegment(String text, bool isSelected) {
    return Center(
      child: Text(
        text,
        style: BanaTypography.labelMedium.copyWith(
          color: _getTextColor(isSelected),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildCustomSegment(BanaSegment segment, bool isSelected) {
    final textColor = _getTextColor(isSelected);
    
    if (segment.icon != null && segment.text != null) {
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              segment.icon,
              size: 16,
              color: textColor,
            ),
            BanaSpacing.horizontalSpacing.xs,
            Text(
              segment.text!,
              style: BanaTypography.labelMedium.copyWith(
                color: textColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else if (segment.icon != null) {
      return Center(
        child: Icon(
          segment.icon,
          size: 20,
          color: textColor,
        ),
      );
    } else if (segment.text != null) {
      return _buildTextSegment(segment.text!, isSelected);
    } else {
      return const SizedBox.shrink();
    }
  }

  void _onSegmentTap(T value) {
    if (value != widget.selectedValue) {
      HapticFeedback.lightImpact();
      widget.onSelectionChanged(value);
    }
  }

  Color _getBackgroundColor() {
    switch (widget.variant) {
      case BanaSegmentedControlVariant.filled:
        return BanaColors.borderLight;
      case BanaSegmentedControlVariant.outlined:
        return BanaColors.surface;
    }
  }

  Color _getSelectedColor() {
    if (widget.selectedColor != null) return widget.selectedColor!;
    
    switch (widget.variant) {
      case BanaSegmentedControlVariant.filled:
        return BanaColors.surface;
      case BanaSegmentedControlVariant.outlined:
        return BanaColors.primary.withValues(alpha: 0.1);
    }
  }

  Color _getTextColor(bool isSelected) {
    if (isSelected && widget.selectedTextColor != null) {
      return widget.selectedTextColor!;
    }
    if (!isSelected && widget.textColor != null) {
      return widget.textColor!;
    }
    
    switch (widget.variant) {
      case BanaSegmentedControlVariant.filled:
        return isSelected ? BanaColors.text : BanaColors.textSecondary;
      case BanaSegmentedControlVariant.outlined:
        return isSelected ? BanaColors.primary : BanaColors.textSecondary;
    }
  }
}

/// Segment content with optional icon and text
class BanaSegment {
  /// Segment text
  final String? text;
  
  /// Segment icon
  final IconData? icon;

  const BanaSegment({
    this.text,
    this.icon,
  }) : assert(text != null || icon != null, 'Either text or icon must be provided');
}

/// Segmented control variant types
enum BanaSegmentedControlVariant {
  /// Filled variant with background color
  filled,
  
  /// Outlined variant with border
  outlined,
}
