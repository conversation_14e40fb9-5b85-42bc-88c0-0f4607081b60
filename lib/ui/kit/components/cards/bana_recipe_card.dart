import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Recipe Card Component
/// 
/// Recipe card component following the BanaChef Design System.
/// Displays recipe information with image, title, metadata, and actions.
/// 
/// Usage:
/// ```dart
/// BanaRecipeCard(
///   imageUrl: 'https://example.com/recipe.jpg',
///   title: 'Delicious Pasta',
///   cookingTime: '30 min',
///   difficulty: 'Easy',
///   servings: 4,
///   isFavorite: false,
///   onTap: () => Navigator.push(...),
///   onFavoriteToggle: (isFavorite) => saveFavorite(isFavorite),
/// )
/// ```
class BanaRecipeCard extends StatefulWidget {
  /// Recipe image URL
  final String? imageUrl;
  
  /// Recipe title
  final String title;
  
  /// Cooking time (e.g., "30 min")
  final String? cookingTime;
  
  /// Difficulty level (e.g., "Easy", "Medium", "Hard")
  final String? difficulty;
  
  /// Number of servings
  final int? servings;
  
  /// Whether recipe is marked as favorite
  final bool isFavorite;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Callback when favorite button is toggled
  final ValueChanged<bool>? onFavoriteToggle;
  
  /// Optional rating (0.0 to 5.0)
  final double? rating;
  
  /// Optional author name
  final String? author;
  
  /// Card variant
  final BanaRecipeCardVariant variant;
  
  /// Custom aspect ratio for image
  final double aspectRatio;

  const BanaRecipeCard({
    super.key,
    this.imageUrl,
    required this.title,
    this.cookingTime,
    this.difficulty,
    this.servings,
    this.isFavorite = false,
    this.onTap,
    this.onFavoriteToggle,
    this.rating,
    this.author,
    this.variant = BanaRecipeCardVariant.standard,
    this.aspectRatio = 16 / 9,
  });

  @override
  State<BanaRecipeCard> createState() => _BanaRecipeCardState();
}

class _BanaRecipeCardState extends State<BanaRecipeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final elevationValue = _isPressed ? 
            BanaShadows.cardHover : BanaShadows.card;
        
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(
                color: BanaColors.surface,
                borderRadius: BanaBorders.radius.lg,
                boxShadow: elevationValue,
              ),
              child: _buildCardContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardContent() {
    switch (widget.variant) {
      case BanaRecipeCardVariant.standard:
        return _buildStandardCard();
      case BanaRecipeCardVariant.compact:
        return _buildCompactCard();
      case BanaRecipeCardVariant.featured:
        return _buildFeaturedCard();
    }
  }

  Widget _buildStandardCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image section
        _buildImageSection(),
        
        // Content section
        Padding(
          padding: BanaSpacing.all.md,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and favorite button
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: BanaTypography.headline,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (widget.onFavoriteToggle != null)
                    _buildFavoriteButton(),
                ],
              ),
              
              // Author (if provided)
              if (widget.author != null) ...[
                BanaSpacing.verticalSpacing.xs,
                Text(
                  'by ${widget.author}',
                  style: BanaTypography.caption,
                ),
              ],
              
              // Rating (if provided)
              if (widget.rating != null) ...[
                BanaSpacing.verticalSpacing.sm,
                _buildRatingSection(),
              ],
              
              // Metadata
              BanaSpacing.verticalSpacing.sm,
              _buildMetadataSection(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompactCard() {
    return Padding(
      padding: BanaSpacing.all.md,
      child: Row(
        children: [
          // Image
          if (widget.imageUrl != null)
            ClipRRect(
              borderRadius: BanaBorders.radius.sm,
              child: SizedBox(
                width: 80,
                height: 80,
                child: _buildImage(),
              ),
            ),
          
          if (widget.imageUrl != null) BanaSpacing.horizontalSpacing.md,
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and favorite
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.title,
                        style: BanaTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (widget.onFavoriteToggle != null)
                      _buildFavoriteButton(size: BanaIconButtonSize.small),
                  ],
                ),
                
                BanaSpacing.verticalSpacing.xs,
                
                // Metadata
                _buildMetadataSection(compact: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedCard() {
    return Stack(
      children: [
        // Background image
        if (widget.imageUrl != null)
          ClipRRect(
            borderRadius: BanaBorders.radius.lg,
            child: AspectRatio(
              aspectRatio: widget.aspectRatio,
              child: _buildImage(),
            ),
          ),
        
        // Gradient overlay
        ClipRRect(
          borderRadius: BanaBorders.radius.lg,
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Color(0x80000000),
                  ],
                ),
              ),
            ),
          ),
        ),
        
        // Content overlay
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Padding(
            padding: BanaSpacing.all.md,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and favorite
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.title,
                        style: BanaTypography.title3.copyWith(
                          color: BanaColors.surface,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (widget.onFavoriteToggle != null)
                      _buildFavoriteButton(
                        iconColor: BanaColors.surface,
                        backgroundColor: Colors.black26,
                      ),
                  ],
                ),
                
                BanaSpacing.verticalSpacing.sm,
                
                // Metadata
                _buildMetadataSection(
                  textColor: BanaColors.surface.withValues(alpha: 0.9),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    if (widget.imageUrl == null) return const SizedBox.shrink();
    
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(BanaBorders.radiusLG),
      ),
      child: AspectRatio(
        aspectRatio: widget.aspectRatio,
        child: _buildImage(),
      ),
    );
  }

  Widget _buildImage() {
    return Image.network(
      widget.imageUrl!,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: BanaColors.borderLight,
          child: const Icon(
            Icons.restaurant,
            size: 48,
            color: BanaColors.textTertiary,
          ),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: BanaColors.borderLight,
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(BanaColors.primary),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFavoriteButton({
    BanaIconButtonSize size = BanaIconButtonSize.medium,
    Color? iconColor,
    Color? backgroundColor,
  }) {
    return BanaIconButton(
      icon: widget.isFavorite ? Icons.favorite : Icons.favorite_border,
      onPressed: () => widget.onFavoriteToggle?.call(!widget.isFavorite),
      size: size,
      iconColor: iconColor ?? (widget.isFavorite ? BanaColors.error : BanaColors.textSecondary),
      backgroundColor: backgroundColor,
      accessibilityLabel: widget.isFavorite ? 'Remove from favorites' : 'Add to favorites',
    );
  }

  Widget _buildRatingSection() {
    if (widget.rating == null) return const SizedBox.shrink();
    
    return Row(
      children: [
        ...List.generate(5, (index) {
          final starValue = index + 1;
          return Icon(
            starValue <= widget.rating! ? Icons.star : Icons.star_border,
            size: 16,
            color: BanaColors.warning,
          );
        }),
        BanaSpacing.horizontalSpacing.xs,
        Text(
          widget.rating!.toStringAsFixed(1),
          style: BanaTypography.caption,
        ),
      ],
    );
  }

  Widget _buildMetadataSection({
    bool compact = false,
    Color? textColor,
  }) {
    final metadata = <Widget>[];
    final style = (compact ? BanaTypography.labelSmall : BanaTypography.caption)
        .copyWith(color: textColor);
    
    if (widget.cookingTime != null) {
      metadata.add(_buildMetadataItem(
        icon: Icons.access_time,
        text: widget.cookingTime!,
        style: style,
      ));
    }
    
    if (widget.difficulty != null) {
      metadata.add(_buildMetadataItem(
        icon: Icons.bar_chart,
        text: widget.difficulty!,
        style: style,
      ));
    }
    
    if (widget.servings != null) {
      metadata.add(_buildMetadataItem(
        icon: Icons.people,
        text: '${widget.servings} servings',
        style: style,
      ));
    }
    
    if (metadata.isEmpty) return const SizedBox.shrink();
    
    return Wrap(
      spacing: compact ? BanaSpacing.sm : BanaSpacing.md,
      runSpacing: BanaSpacing.xs,
      children: metadata,
    );
  }

  Widget _buildMetadataItem({
    required IconData icon,
    required String text,
    required TextStyle style,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: style.color,
        ),
        BanaSpacing.horizontalSpacing.xs,
        Text(text, style: style),
      ],
    );
  }
}

/// Recipe card variant types
enum BanaRecipeCardVariant {
  /// Standard card with image on top
  standard,
  
  /// Compact horizontal layout
  compact,
  
  /// Featured card with overlay content
  featured,
}
