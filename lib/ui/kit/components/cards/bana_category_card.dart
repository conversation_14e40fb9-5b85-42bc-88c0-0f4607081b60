import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';

/// BanaChef AI Category Card Component
/// 
/// Category card component following the BanaChef Design System.
/// Represents food categories with visual appeal and clear hierarchy.
/// 
/// Usage:
/// ```dart
/// BanaCategoryCard(
///   title: 'Italian Cuisine',
///   imageUrl: 'https://example.com/italian.jpg',
///   recipeCount: 24,
///   onTap: () => Navigator.push(...),
/// )
/// 
/// // Icon-based category
/// BanaCategoryCard.icon(
///   title: 'Desserts',
///   icon: Icons.cake,
///   backgroundColor: Colors.pink.shade50,
///   iconColor: Colors.pink,
///   onTap: () {},
/// )
/// ```
class BanaCategoryCard extends StatefulWidget {
  /// Category title
  final String title;
  
  /// Category description (optional)
  final String? description;
  
  /// Category image URL
  final String? imageUrl;
  
  /// Category icon (alternative to image)
  final IconData? icon;
  
  /// Number of recipes in category
  final int? recipeCount;
  
  /// Background color (for icon-based cards)
  final Color? backgroundColor;
  
  /// Icon color (for icon-based cards)
  final Color? iconColor;
  
  /// Text color override
  final Color? textColor;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Card variant
  final BanaCategoryCardVariant variant;
  
  /// Card aspect ratio
  final double aspectRatio;

  const BanaCategoryCard({
    super.key,
    required this.title,
    this.description,
    this.imageUrl,
    this.icon,
    this.recipeCount,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.onTap,
    this.variant = BanaCategoryCardVariant.standard,
    this.aspectRatio = 1.0,
  });

  /// Icon-based category card constructor
  const BanaCategoryCard.icon({
    super.key,
    required this.title,
    this.description,
    required this.icon,
    this.recipeCount,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.onTap,
    this.variant = BanaCategoryCardVariant.icon,
    this.aspectRatio = 1.0,
  }) : imageUrl = null;

  /// Horizontal category card constructor
  const BanaCategoryCard.horizontal({
    super.key,
    required this.title,
    this.description,
    this.imageUrl,
    this.icon,
    this.recipeCount,
    this.backgroundColor,
    this.iconColor,
    this.textColor,
    this.onTap,
    this.aspectRatio = 2.5,
  }) : variant = BanaCategoryCardVariant.horizontal;

  @override
  State<BanaCategoryCard> createState() => _BanaCategoryCardState();
}

class _BanaCategoryCardState extends State<BanaCategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.97,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final elevationValue = _isPressed ? 
            BanaShadows.cardHover : BanaShadows.card;
        
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? BanaColors.surface,
                borderRadius: BanaBorders.radius.lg,
                boxShadow: elevationValue,
              ),
              child: _buildCardContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardContent() {
    switch (widget.variant) {
      case BanaCategoryCardVariant.standard:
        return _buildStandardCard();
      case BanaCategoryCardVariant.icon:
        return _buildIconCard();
      case BanaCategoryCardVariant.horizontal:
        return _buildHorizontalCard();
    }
  }

  Widget _buildStandardCard() {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Stack(
        children: [
          // Background image or color
          ClipRRect(
            borderRadius: BanaBorders.radius.lg,
            child: SizedBox.expand(
              child: widget.imageUrl != null
                  ? _buildImage()
                  : Container(
                      decoration: BoxDecoration(
                        gradient: _getDefaultGradient(),
                      ),
                    ),
            ),
          ),
          
          // Gradient overlay for better text readability
          if (widget.imageUrl != null)
            ClipRRect(
              borderRadius: BanaBorders.radius.lg,
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Color(0x60000000),
                    ],
                  ),
                ),
              ),
            ),
          
          // Content overlay
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Padding(
              padding: BanaSpacing.all.md,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.title,
                    style: (widget.imageUrl != null 
                        ? BanaTypography.title3.copyWith(color: BanaColors.surface)
                        : BanaTypography.title3.copyWith(color: widget.textColor ?? BanaColors.text)),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  if (widget.description != null) ...[
                    BanaSpacing.verticalSpacing.xs,
                    Text(
                      widget.description!,
                      style: (widget.imageUrl != null
                          ? BanaTypography.bodySmall.copyWith(color: BanaColors.surface.withValues(alpha: 0.9))
                          : BanaTypography.bodySmall.copyWith(color: widget.textColor?.withValues(alpha: 0.7) ?? BanaColors.textSecondary)),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  
                  if (widget.recipeCount != null) ...[
                    BanaSpacing.verticalSpacing.xs,
                    Text(
                      '${widget.recipeCount} recipes',
                      style: (widget.imageUrl != null
                          ? BanaTypography.caption.copyWith(color: BanaColors.surface.withValues(alpha: 0.8))
                          : BanaTypography.caption.copyWith(color: widget.textColor?.withValues(alpha: 0.6) ?? BanaColors.textSecondary)),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIconCard() {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Padding(
        padding: BanaSpacing.all.md,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            if (widget.icon != null)
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: (widget.iconColor ?? BanaColors.primary).withValues(alpha: 0.1),
                  borderRadius: BanaBorders.radius.lg,
                ),
                child: Icon(
                  widget.icon,
                  size: 32,
                  color: widget.iconColor ?? BanaColors.primary,
                ),
              ),
            
            BanaSpacing.verticalSpacing.md,
            
            // Title
            Text(
              widget.title,
              style: BanaTypography.headline.copyWith(
                color: widget.textColor ?? BanaColors.text,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            // Description
            if (widget.description != null) ...[
              BanaSpacing.verticalSpacing.xs,
              Text(
                widget.description!,
                style: BanaTypography.bodySmall.copyWith(
                  color: widget.textColor?.withValues(alpha: 0.7) ?? BanaColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            
            // Recipe count
            if (widget.recipeCount != null) ...[
              BanaSpacing.verticalSpacing.xs,
              Text(
                '${widget.recipeCount} recipes',
                style: BanaTypography.caption.copyWith(
                  color: widget.textColor?.withValues(alpha: 0.6) ?? BanaColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHorizontalCard() {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Row(
        children: [
          // Image or icon section
          Expanded(
            flex: 2,
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(BanaBorders.radiusLG),
              ),
              child: SizedBox(
                height: double.infinity,
                child: widget.imageUrl != null
                    ? _buildImage()
                    : widget.icon != null
                        ? Container(
                            decoration: BoxDecoration(
                              gradient: _getDefaultGradient(),
                            ),
                            child: Icon(
                              widget.icon,
                              size: 48,
                              color: widget.iconColor ?? BanaColors.surface,
                            ),
                          )
                        : Container(
                            decoration: BoxDecoration(
                              gradient: _getDefaultGradient(),
                            ),
                          ),
              ),
            ),
          ),
          
          // Content section
          Expanded(
            flex: 3,
            child: Padding(
              padding: BanaSpacing.all.md,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.title,
                    style: BanaTypography.headline.copyWith(
                      color: widget.textColor ?? BanaColors.text,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  if (widget.description != null) ...[
                    BanaSpacing.verticalSpacing.xs,
                    Text(
                      widget.description!,
                      style: BanaTypography.bodySmall.copyWith(
                        color: widget.textColor?.withValues(alpha: 0.7) ?? BanaColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  
                  if (widget.recipeCount != null) ...[
                    BanaSpacing.verticalSpacing.xs,
                    Text(
                      '${widget.recipeCount} recipes',
                      style: BanaTypography.caption.copyWith(
                        color: widget.textColor?.withValues(alpha: 0.6) ?? BanaColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImage() {
    return Image.network(
      widget.imageUrl!,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          decoration: BoxDecoration(
            gradient: _getDefaultGradient(),
          ),
          child: const Icon(
            Icons.restaurant_menu,
            size: 48,
            color: BanaColors.surface,
          ),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          decoration: BoxDecoration(
            gradient: _getDefaultGradient(),
          ),
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(BanaColors.surface),
            ),
          ),
        );
      },
    );
  }

  LinearGradient _getDefaultGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        BanaColors.primary,
        BanaColors.primary.withValues(alpha: 0.8),
      ],
    );
  }
}

/// Category card variant types
enum BanaCategoryCardVariant {
  /// Standard card with image/gradient background
  standard,
  
  /// Icon-based card with centered layout
  icon,
  
  /// Horizontal layout with image on left
  horizontal,
}
