import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/bana_colors.dart';
import '../../tokens/bana_typography.dart';
import '../../tokens/bana_spacing.dart';
import '../../tokens/bana_shadows.dart';
import '../../tokens/bana_borders.dart';
import '../buttons/bana_icon_button.dart';

/// BanaChef AI Pantry Card Component
/// 
/// Pantry item card component following the BanaChef Design System.
/// Displays pantry items with quantity, expiration status, and actions.
/// 
/// Usage:
/// ```dart
/// BanaPantryCard(
///   name: 'Tomatoes',
///   quantity: '2 kg',
///   expirationDate: DateTime.now().add(Duration(days: 3)),
///   imageUrl: 'https://example.com/tomato.jpg',
///   onTap: () => editItem(),
///   onEdit: () => showEditDialog(),
///   onDelete: () => deleteItem(),
/// )
/// ```
class BanaPantryCard extends StatefulWidget {
  /// Item name
  final String name;
  
  /// Item quantity (e.g., "2 kg", "1 bottle")
  final String? quantity;
  
  /// Item category (e.g., "Vegetables", "Dairy")
  final String? category;
  
  /// Expiration date
  final DateTime? expirationDate;
  
  /// Item image URL
  final String? imageUrl;
  
  /// Item icon (alternative to image)
  final IconData? icon;
  
  /// Background color for icon-based items
  final Color? backgroundColor;
  
  /// Icon color for icon-based items
  final Color? iconColor;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Callback when edit action is triggered
  final VoidCallback? onEdit;
  
  /// Callback when delete action is triggered
  final VoidCallback? onDelete;
  
  /// Card variant
  final BanaPantryCardVariant variant;
  
  /// Whether to show swipe actions
  final bool showSwipeActions;

  const BanaPantryCard({
    super.key,
    required this.name,
    this.quantity,
    this.category,
    this.expirationDate,
    this.imageUrl,
    this.icon,
    this.backgroundColor,
    this.iconColor,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.variant = BanaPantryCardVariant.standard,
    this.showSwipeActions = true,
  });

  @override
  State<BanaPantryCard> createState() => _BanaPantryCardState();
}

class _BanaPantryCardState extends State<BanaPantryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _onTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showSwipeActions && (widget.onEdit != null || widget.onDelete != null)) {
      return _buildSwipeableCard();
    }
    
    return _buildCard();
  }

  Widget _buildSwipeableCard() {
    return Dismissible(
      key: Key(widget.name),
      direction: DismissDirection.endToStart,
      background: _buildSwipeBackground(),
      confirmDismiss: (direction) async {
        // Don't actually dismiss, just trigger action
        if (widget.onDelete != null) {
          widget.onDelete!();
        }
        return false;
      },
      child: _buildCard(),
    );
  }

  Widget _buildSwipeBackground() {
    return Container(
      decoration: BoxDecoration(
        color: BanaColors.error,
        borderRadius: BanaBorders.radius.lg,
      ),
      alignment: Alignment.centerRight,
      padding: BanaSpacing.horizontal.lg,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (widget.onEdit != null) ...[
            GestureDetector(
              onTap: widget.onEdit,
              child: Container(
                width: 48,
                height: 48,
                decoration: const BoxDecoration(
                  color: Colors.white24,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.edit,
                  color: BanaColors.surface,
                  size: 24,
                ),
              ),
            ),
            BanaSpacing.horizontalSpacing.md,
          ],
          if (widget.onDelete != null)
            const Icon(
              Icons.delete,
              color: BanaColors.surface,
              size: 24,
            ),
        ],
      ),
    );
  }

  Widget _buildCard() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(
                color: BanaColors.surface,
                borderRadius: BanaBorders.radius.lg,
                boxShadow: BanaShadows.card,
                border: _getExpirationBorder(),
              ),
              child: _buildCardContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardContent() {
    switch (widget.variant) {
      case BanaPantryCardVariant.standard:
        return _buildStandardCard();
      case BanaPantryCardVariant.compact:
        return _buildCompactCard();
    }
  }

  Widget _buildStandardCard() {
    return Padding(
      padding: BanaSpacing.all.md,
      child: Row(
        children: [
          // Image or icon
          _buildItemVisual(),
          
          BanaSpacing.horizontalSpacing.md,
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name and actions
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.name,
                        style: BanaTypography.headline,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!widget.showSwipeActions && widget.onEdit != null)
                      BanaIconButton(
                        icon: Icons.more_vert,
                        onPressed: widget.onEdit!,
                        size: BanaIconButtonSize.small,
                        accessibilityLabel: 'More options',
                      ),
                  ],
                ),
                
                // Category
                if (widget.category != null) ...[
                  BanaSpacing.verticalSpacing.xs,
                  Text(
                    widget.category!,
                    style: BanaTypography.caption,
                  ),
                ],
                
                // Quantity and expiration
                BanaSpacing.verticalSpacing.xs,
                Row(
                  children: [
                    if (widget.quantity != null) ...[
                      Text(
                        widget.quantity!,
                        style: BanaTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (widget.expirationDate != null)
                        Text(
                          ' • ',
                          style: BanaTypography.bodyMedium.copyWith(
                            color: BanaColors.textSecondary,
                          ),
                        ),
                    ],
                    if (widget.expirationDate != null)
                      _buildExpirationText(),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactCard() {
    return Padding(
      padding: BanaSpacing.all.sm,
      child: Row(
        children: [
          // Small image or icon
          SizedBox(
            width: 40,
            height: 40,
            child: _buildItemVisual(size: 40),
          ),
          
          BanaSpacing.horizontalSpacing.sm,
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.name,
                  style: BanaTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                
                if (widget.quantity != null || widget.expirationDate != null) ...[
                  BanaSpacing.verticalSpacing.xs,
                  Row(
                    children: [
                      if (widget.quantity != null) ...[
                        Text(
                          widget.quantity!,
                          style: BanaTypography.caption,
                        ),
                        if (widget.expirationDate != null)
                          Text(
                            ' • ',
                            style: BanaTypography.caption.copyWith(
                              color: BanaColors.textSecondary,
                            ),
                          ),
                      ],
                      if (widget.expirationDate != null)
                        _buildExpirationText(compact: true),
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          // Status indicator
          _buildStatusIndicator(),
        ],
      ),
    );
  }

  Widget _buildItemVisual({double size = 56}) {
    if (widget.imageUrl != null) {
      return ClipRRect(
        borderRadius: BanaBorders.radius.sm,
        child: Image.network(
          widget.imageUrl!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildIconContainer(size: size);
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return _buildIconContainer(size: size, isLoading: true);
          },
        ),
      );
    }
    
    return _buildIconContainer(size: size);
  }

  Widget _buildIconContainer({double size = 56, bool isLoading = false}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? BanaColors.primary.withValues(alpha: 0.1),
        borderRadius: BanaBorders.radius.sm,
      ),
      child: isLoading
          ? const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(BanaColors.primary),
                ),
              ),
            )
          : Icon(
              widget.icon ?? Icons.inventory_2,
              size: size * 0.5,
              color: widget.iconColor ?? BanaColors.primary,
            ),
    );
  }

  Widget _buildExpirationText({bool compact = false}) {
    if (widget.expirationDate == null) return const SizedBox.shrink();
    
    final now = DateTime.now();
    final difference = widget.expirationDate!.difference(now).inDays;
    final isExpired = difference < 0;
    final isExpiringSoon = difference <= 3 && difference >= 0;
    
    String text;
    Color color;
    
    if (isExpired) {
      text = 'Expired';
      color = BanaColors.error;
    } else if (isExpiringSoon) {
      text = difference == 0 ? 'Expires today' : 'Expires in $difference days';
      color = BanaColors.warning;
    } else {
      text = 'Expires in $difference days';
      color = BanaColors.textSecondary;
    }
    
    return Text(
      text,
      style: (compact ? BanaTypography.caption : BanaTypography.bodySmall).copyWith(
        color: color,
        fontWeight: isExpired || isExpiringSoon ? FontWeight.w600 : null,
      ),
    );
  }

  Widget _buildStatusIndicator() {
    if (widget.expirationDate == null) return const SizedBox.shrink();
    
    final now = DateTime.now();
    final difference = widget.expirationDate!.difference(now).inDays;
    final isExpired = difference < 0;
    final isExpiringSoon = difference <= 3 && difference >= 0;
    
    Color color;
    if (isExpired) {
      color = BanaColors.error;
    } else if (isExpiringSoon) {
      color = BanaColors.warning;
    } else {
      color = BanaColors.success;
    }
    
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Border? _getExpirationBorder() {
    if (widget.expirationDate == null) return null;
    
    final now = DateTime.now();
    final difference = widget.expirationDate!.difference(now).inDays;
    final isExpired = difference < 0;
    final isExpiringSoon = difference <= 3 && difference >= 0;
    
    if (isExpired) {
      return Border.all(color: BanaColors.error.withValues(alpha: 0.3), width: 1);
    } else if (isExpiringSoon) {
      return Border.all(color: BanaColors.warning.withValues(alpha: 0.3), width: 1);
    }
    
    return null;
  }
}

/// Pantry card variant types
enum BanaPantryCardVariant {
  /// Standard card with full information
  standard,
  
  /// Compact card with minimal information
  compact,
}
