import '../../../ui/kit/ui_kit.dart';
import '../../../shared/assets/assets.gen.dart';

/// Header component cho màn hình Referral
/// 
/// Hiển thị mascot Chef <PERSON><PERSON> và tiêu đề thân thiện
/// theo thiế<PERSON> kế BanaChef Design System
class ReferralHeader extends StatelessWidget {
  const ReferralHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      mobile: _buildContent(context, 100),
      tablet: _buildContent(context, 120),
      desktop: _buildContent(context, 140),
    );
  }

  Widget _buildContent(BuildContext context, double imageSize) {
    return Column(
      children: [
        // Mascot Chef Bana illustration
        Container(
          width: imageSize,
          height: imageSize,
          margin: BanaSpacing.all.lg,
          child: Assets.images.app.referralIllus.svg(
            width: imageSize,
            height: imageSize,
            fit: BoxFit.contain,
          ),
        ),

        BanaSpacing.verticalSpacing.md,

        // Tiêu đề thân thiện
        Text(
          'Có ai giới thiệu bạn không?',
          style: BanaTypography.title2.copyWith(
            color: BanaColors.text,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
