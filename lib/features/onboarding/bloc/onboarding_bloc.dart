import 'dart:async';
import '../../../core/bloc/bloc_exports.dart';
import '../models/onboarding_data.dart';
import '../models/onboarding_constants.dart';
import 'onboarding_event.dart';
import 'onboarding_state.dart';

/// BLoC for managing onboarding flow
class OnboardingBloc extends BaseBloc<OnboardingEvent, OnboardingState> {
  OnboardingBloc() : super(const OnboardingInitial()) {
    on<StartOnboardingEvent>(_onStartOnboarding);
    on<NextStepEvent>(_onNextStep);
    on<PreviousStepEvent>(_onPreviousStep);
    on<SkipStepEvent>(_onSkipStep);
    on<GoToStepEvent>(_onGoToStep);
    on<UpdateGenderEvent>(_onUpdateGender);
    on<UpdateHeightWeightEvent>(_onUpdateHeightWeight);
    on<UpdateBirthDateEvent>(_onUpdateBirthDate);
    on<UpdateAllergiesEvent>(_onUpdateAllergies);
    on<UpdateMedicalConditionsEvent>(_onUpdateMedicalConditions);
    on<UpdatePregnancyStatusEvent>(_onUpdatePregnancyStatus);
    on<UpdateFavoriteFoodEvent>(_onUpdateFavoriteFood);
    on<UpdateDislikedFoodEvent>(_onUpdateDislikedFood);
    on<UpdateSpiceLevelEvent>(_onUpdateSpiceLevel);
    on<UpdateTexturePreferencesEvent>(_onUpdateTexturePreferences);
    on<UpdateCuisinePreferencesEvent>(_onUpdateCuisinePreferences);
    on<UpdateDietaryLifestyleEvent>(_onUpdateDietaryLifestyle);
    on<UpdateCookingSkillTimeEvent>(_onUpdateCookingSkillTime);
    on<UpdateKitchenEquipmentEvent>(_onUpdateKitchenEquipment);
    on<UpdateMealSuggestionsEvent>(_onUpdateMealSuggestions);
    on<CompleteOnboardingEvent>(_onCompleteOnboarding);

    // Authentication events
    on<ShowAuthBottomSheetEvent>(_onShowAuthBottomSheet);
    on<AppleSignInEvent>(_onAppleSignIn);
    on<GoogleSignInEvent>(_onGoogleSignIn);

    on<AuthSuccessEvent>(_onAuthSuccess);
    on<AuthFailureEvent>(_onAuthFailure);
  }

  /// Start onboarding flow
  Future<void> _onStartOnboarding(
    StartOnboardingEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    try {
      emit(const OnboardingLoading());
      
      // Initialize with empty data and first step
      const data = OnboardingData();
      const currentStepIndex = 0;
      final allSteps = OnboardingConstants.steps;
      
      emit(OnboardingInProgress(
        data: data,
        currentStepIndex: currentStepIndex,
        allSteps: allSteps,
        canGoNext: false,
        canGoPrevious: false,
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to start onboarding: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Go to next step
  Future<void> _onNextStep(
    NextStepEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final filteredSteps = currentState.filteredSteps;
      final currentFilteredIndex = filteredSteps.indexWhere(
        (step) => step.id == currentState.currentStep.id,
      );
      
      if (currentFilteredIndex < filteredSteps.length - 1) {
        // Move to next step in filtered list
        final nextStep = filteredSteps[currentFilteredIndex + 1];
        final nextStepIndex = currentState.allSteps.indexWhere(
          (step) => step.id == nextStep.id,
        );
        
        emit(currentState.copyWith(
          currentStepIndex: nextStepIndex,
          canGoNext: _canGoNext(currentState.data, nextStep),
          canGoPrevious: true,
        ));
      } else {
        // Complete onboarding
        add(const CompleteOnboardingEvent());
      }
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to go to next step: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Go to previous step
  Future<void> _onPreviousStep(
    PreviousStepEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final filteredSteps = currentState.filteredSteps;
      final currentFilteredIndex = filteredSteps.indexWhere(
        (step) => step.id == currentState.currentStep.id,
      );
      
      if (currentFilteredIndex > 0) {
        // Move to previous step in filtered list
        final previousStep = filteredSteps[currentFilteredIndex - 1];
        final previousStepIndex = currentState.allSteps.indexWhere(
          (step) => step.id == previousStep.id,
        );
        
        emit(currentState.copyWith(
          currentStepIndex: previousStepIndex,
          canGoNext: _canGoNext(currentState.data, previousStep),
          canGoPrevious: previousStepIndex > 0,
        ));
      }
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to go to previous step: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Skip current step
  Future<void> _onSkipStep(
    SkipStepEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    // For now, skip is same as next
    add(const NextStepEvent());
  }

  /// Go to specific step
  Future<void> _onGoToStep(
    GoToStepEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      if (event.stepIndex >= 0 && event.stepIndex < currentState.allSteps.length) {
        final targetStep = currentState.allSteps[event.stepIndex];
        
        emit(currentState.copyWith(
          currentStepIndex: event.stepIndex,
          canGoNext: _canGoNext(currentState.data, targetStep),
          canGoPrevious: event.stepIndex > 0,
        ));
      }
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to go to step ${event.stepIndex}: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update gender
  Future<void> _onUpdateGender(
    UpdateGenderEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(gender: event.gender);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update gender: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update height and weight
  Future<void> _onUpdateHeightWeight(
    UpdateHeightWeightEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(
        height: event.height ?? currentState.data.height,
        weight: event.weight ?? currentState.data.weight,
      );
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update height/weight: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update birth date
  Future<void> _onUpdateBirthDate(
    UpdateBirthDateEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(birthDate: event.birthDate);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update birth date: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update allergies
  Future<void> _onUpdateAllergies(
    UpdateAllergiesEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(allergies: event.allergies);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update allergies: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update medical conditions
  Future<void> _onUpdateMedicalConditions(
    UpdateMedicalConditionsEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(medicalConditions: event.medicalConditions);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update medical conditions: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update pregnancy status
  Future<void> _onUpdatePregnancyStatus(
    UpdatePregnancyStatusEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(pregnancyStatus: event.pregnancyStatus);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update pregnancy status: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update favorite food
  Future<void> _onUpdateFavoriteFood(
    UpdateFavoriteFoodEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(favoriteFood: event.favoriteFood);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update favorite food: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update disliked food
  Future<void> _onUpdateDislikedFood(
    UpdateDislikedFoodEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(dislikedFood: event.dislikedFood);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update disliked food: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update spice level (0-5)
  Future<void> _onUpdateSpiceLevel(
    UpdateSpiceLevelEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(
        spiceLevel: event.spiceLevel,
      );
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update spice level: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update texture preferences
  Future<void> _onUpdateTexturePreferences(
    UpdateTexturePreferencesEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(
        texturePreferences: event.texturePreferences,
      );
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update texture preferences: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update cuisine preferences
  Future<void> _onUpdateCuisinePreferences(
    UpdateCuisinePreferencesEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(cuisinePreferences: event.cuisinePreferences);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update cuisine preferences: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update dietary lifestyle
  Future<void> _onUpdateDietaryLifestyle(
    UpdateDietaryLifestyleEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(dietaryLifestyles: event.dietaryLifestyles);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update dietary lifestyle: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update cooking skill and time
  Future<void> _onUpdateCookingSkillTime(
    UpdateCookingSkillTimeEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(
        cookingSkill: event.cookingSkill ?? currentState.data.cookingSkill,
        cookingTime: event.cookingTime ?? currentState.data.cookingTime,
      );
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update cooking skill/time: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update kitchen equipment
  Future<void> _onUpdateKitchenEquipment(
    UpdateKitchenEquipmentEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(kitchenEquipment: event.kitchenEquipment);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update kitchen equipment: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Update meal suggestions
  Future<void> _onUpdateMealSuggestions(
    UpdateMealSuggestionsEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      final updatedData = currentState.data.copyWith(mealSuggestions: event.mealSuggestions);
      emit(currentState.copyWith(
        data: updatedData,
        canGoNext: _canGoNext(updatedData, currentState.currentStep),
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to update meal suggestions: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Complete onboarding
  Future<void> _onCompleteOnboarding(
    CompleteOnboardingEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      emit(const OnboardingLoading());

      // Here you would typically save the data to a repository
      // For now, just emit completed state
      // Trigger auth bottom sheet directly
      add(const ShowAuthBottomSheetEvent());
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to complete onboarding: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Check if user can go to next step based on current data and step
  bool _canGoNext(OnboardingData data, OnboardingStep step) {
    switch (step.id) {
      case 'gender':
        return data.gender != null;
      case 'height_weight':
        return data.height != null && data.weight != null;
      case 'birth_date':
        return data.birthDate != null;
      case 'allergies':
        return data.allergies.isNotEmpty;
      case 'medical_conditions':
        return data.medicalConditions.isNotEmpty;
      case 'pregnancy_status':
        return data.pregnancyStatus != null;
      case 'favorite_food':
        return data.favoriteFood.isNotEmpty;
      case 'disliked_food':
        return data.dislikedFood.isNotEmpty;
      case 'spice_texture':
        return data.texturePreferences.isNotEmpty;
      case 'cuisine_preferences':
        return data.cuisinePreferences.isNotEmpty;
      case 'dietary_lifestyle':
        return data.dietaryLifestyles.isNotEmpty;
      case 'cooking_skill':
        return data.cookingSkill != null;
      case 'cooking_time':
        return data.cookingTime != null;
      case 'kitchen_equipment':
        return data.kitchenEquipment.isNotEmpty;
      case 'meal_suggestions':
        return data.mealSuggestions.isNotEmpty;
      default:
        return true; // Allow skip for unknown steps
    }
  }

  /// Show authentication bottom sheet
  Future<void> _onShowAuthBottomSheet(
    ShowAuthBottomSheetEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      // Keep current state, bottom sheet will be shown by UI
      // No state change needed here
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to show auth bottom sheet: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Handle Apple Sign In
  Future<void> _onAppleSignIn(
    AppleSignInEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      emit(OnboardingAuthenticating(currentState.data, 'apple'));

      // TODO: Implement actual Apple Sign In logic
      // For now, simulate success after delay
      await Future.delayed(const Duration(seconds: 2));

      // Simulate success
      add(const AuthSuccessEvent(
        userId: 'apple_user_123',
        email: '<EMAIL>',
        displayName: 'Apple User',
      ));
    } catch (error) {
      add(AuthFailureEvent('Apple Sign In failed: $error'));
    }
  }

  /// Handle Google Sign In
  Future<void> _onGoogleSignIn(
    GoogleSignInEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingInProgress) return;

    try {
      emit(OnboardingAuthenticating(currentState.data, 'google'));

      // TODO: Implement actual Google Sign In logic
      // For now, simulate success after delay
      await Future.delayed(const Duration(seconds: 2));

      // Simulate success
      add(const AuthSuccessEvent(
        userId: 'google_user_123',
        email: '<EMAIL>',
        displayName: 'Google User',
      ));
    } catch (error) {
      add(AuthFailureEvent('Google Sign In failed: $error'));
    }
  }



  /// Handle authentication success
  Future<void> _onAuthSuccess(
    AuthSuccessEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingAuthenticating) return;

    try {
      emit(OnboardingCompleted(
        currentState.data,
        userId: event.userId,
        email: event.email,
        displayName: event.displayName,
      ));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to complete authentication: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Handle authentication failure
  Future<void> _onAuthFailure(
    AuthFailureEvent event,
    Emitter<OnboardingState> emit,
  ) async {
    final currentState = state;
    if (currentState is! OnboardingAuthenticating) return;

    try {
      emit(OnboardingAuthError(currentState.data, event.error));
    } catch (error, stackTrace) {
      emit(OnboardingError(
        message: 'Failed to handle auth failure: $error',
        originalError: error,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<void> handleEvent(OnboardingEvent event, Emitter<OnboardingState> emit) async {
    // Events are handled by specific handlers registered in constructor
  }
}
