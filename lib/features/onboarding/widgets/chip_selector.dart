import '../../../ui/kit/ui_kit.dart';

/// Reusable chip selector widget for onboarding
/// Used for allergies, food preferences, etc.
class ChipSelector extends StatefulWidget {
  /// List of available options
  final List<String> options;
  
  /// Currently selected values
  final List<String> selectedValues;
  
  /// Callback when selection changes
  final void Function(List<String> values) onSelectionChanged;
  
  /// Whether to show "Add custom" option
  final bool allowCustom;
  
  /// Placeholder text for custom input
  final String? customPlaceholder;
  
  /// Whether "Không có" option should deselect others
  final bool hasNoneOption;
  
  /// The value that represents "none" (usually "Không có")
  final String? noneValue;

  const ChipSelector({
    super.key,
    required this.options,
    required this.selectedValues,
    required this.onSelectionChanged,
    this.allowCustom = true,
    this.customPlaceholder,
    this.hasNoneOption = true,
    this.noneValue = 'Không có',
  });

  @override
  State<ChipSelector> createState() => _ChipSelectorState();
}

class _ChipSelectorState extends State<ChipSelector> {
  final TextEditingController _customController = TextEditingController();
  bool _showCustomInput = false;
  final List<String> _customOptions = [];

  @override
  void dispose() {
    _customController.dispose();
    super.dispose();
  }

  void _handleSelection(String value) {
    final currentValues = List<String>.from(widget.selectedValues);
    
    if (widget.hasNoneOption && value == widget.noneValue) {
      // If selecting "none", clear all other selections
      if (currentValues.contains(value)) {
        currentValues.remove(value);
      } else {
        currentValues.clear();
        currentValues.add(value);
      }
    } else {
      // If selecting something other than "none", remove "none" if present
      if (widget.hasNoneOption && currentValues.contains(widget.noneValue)) {
        currentValues.remove(widget.noneValue);
      }
      
      // Toggle the selected value
      if (currentValues.contains(value)) {
        currentValues.remove(value);
      } else {
        currentValues.add(value);
      }
    }
    
    widget.onSelectionChanged(currentValues);
  }

  void _addCustomOption() {
    final customValue = _customController.text.trim();
    if (customValue.isNotEmpty && !_allOptions.contains(customValue)) {
      setState(() {
        _customOptions.add(customValue);
        _showCustomInput = false;
        _customController.clear();
      });
      
      // Auto-select the newly added option
      _handleSelection(customValue);
    }
  }

  List<String> get _allOptions => [...widget.options, ..._customOptions];

  @override
  Widget build(BuildContext context) {
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Chips
        Wrap(
          spacing: context.spacingSM,
          runSpacing: context.spacingSM,
          children: [
            // Regular options
            ..._allOptions.map((option) => _buildChip(option)),
            
            // Add custom button
            if (widget.allowCustom && !_showCustomInput)
              _buildAddCustomChip(),
          ],
        ),
        
        // Custom input field
        if (_showCustomInput) ...[
          VSpace.md(),
          Row(
            children: [
              Expanded(
                child: BanaTextField(
                  controller: _customController,
                  hint: widget.customPlaceholder ?? 'Nhập tùy chọn...',
                  onSubmitted: (_) => _addCustomOption(),
                ),
              ),
              BanaSpacing.horizontalSpacing.sm,
              BanaIconButton.filled(
                icon: Icons.add,
                onPressed: _addCustomOption,
                backgroundColor: BanaColors.primary,
                iconColor: BanaColors.onPrimary,
                accessibilityLabel: 'Thêm tùy chọn',
              ),
              BanaSpacing.horizontalSpacing.xs,
              BanaIconButton(
                icon: Icons.close,
                onPressed: () {
                  setState(() {
                    _showCustomInput = false;
                    _customController.clear();
                  });
                },
                accessibilityLabel: 'Đóng',
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildChip(String option) {
    final isSelected = widget.selectedValues.contains(option);
    final isCustom = _customOptions.contains(option);
    
    return GestureDetector(
      onTap: () => _handleSelection(option),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: BanaSpacing.all.md,
        decoration: BoxDecoration(
          color: isSelected
              ? BanaColors.primary
              : BanaColors.surface,
          borderRadius: BanaBorders.radius.circular,
          border: Border.all(
            color: isSelected
                ? BanaColors.primary
                : BanaColors.border,
            width: BanaBorders.widthThin,
          ),
          boxShadow: BanaShadows.elevation1,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              option,
              style: BanaTypography.bodyMedium.copyWith(
                color: isSelected
                    ? BanaColors.onPrimary
                    : BanaColors.text,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            
            // Remove button for custom options
            if (isCustom && isSelected) ...[
              BanaSpacing.horizontalSpacing.xs,
              GestureDetector(
                onTap: () {
                  setState(() {
                    _customOptions.remove(option);
                  });
                  _handleSelection(option); // This will remove it from selection
                },
                child: Icon(
                  Icons.close,
                  size: 16,
                  color: BanaColors.onPrimary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddCustomChip() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showCustomInput = true;
        });
      },
      child: Container(
        padding: BanaSpacing.all.md,
        decoration: BoxDecoration(
          color: BanaColors.surface,
          borderRadius: BanaBorders.radius.circular,
          border: Border.all(
            color: BanaColors.borderLight,
            width: BanaBorders.widthThin,
            style: BorderStyle.solid,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 14,
              color: BanaColors.textTertiary,
            ),
            BanaSpacing.horizontalSpacing.xs,
            Text(
              'Thêm tùy chọn',
              style: BanaTypography.bodySmall.copyWith(
                color: BanaColors.textTertiary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
