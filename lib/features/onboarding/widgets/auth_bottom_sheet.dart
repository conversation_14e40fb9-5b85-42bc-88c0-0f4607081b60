import 'dart:io';
import '../../../ui/kit/ui_kit.dart';
import '../../../core/bloc/bloc_exports.dart';
import '../../../core/di/injection_container.dart';
import '../../auth/cubit/auth_cubit.dart';
import '../../auth/cubit/auth_state.dart';

/// Bottom sheet for authentication with SNS login
class AuthBottomSheet extends StatelessWidget {
  final VoidCallback? onAuthSuccess;
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;

  const AuthBottomSheet({
    super.key,
    this.onAuthSuccess,
    this.onTermsPressed,
    this.onPrivacyPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isIOS = Platform.isIOS;

    return BlocProvider(
      create: (context) => getIt<AuthCubit>(),
      child: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthSuccess || state is AuthAuthenticated) {
            // Close bottom sheet and call success callback
            Navigator.of(context).pop();
            onAuthSuccess?.call();
          } else if (state is AuthError) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              _buildHandle(context),

              // Content
              Padding(
                padding: EdgeInsets.fromLTRB(
                  context.spacingXL,
                  context.spacingMD,
                  context.spacingXL,
                  context.spacingXL,
                ),
                child: Column(
                  children: [
                    // Title
                    _buildTitle(context),

                    VSpace.xl(),

                    // Action buttons
                    _buildActionButtons(context, isIOS),

                    VSpace.xl(),

                    // Legal text
                    _buildLegalText(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHandle(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: context.spacingSM),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      'Chào mừng đến với BanaChef!',
      style: const TextStyle(
        fontFamily: 'Nunito',
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ).copyWith(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isIOS) {
    return Column(
      children: [
        if (isIOS) ...[
          // Apple Sign In (Primary on iOS)
          _buildAppleSignInButton(context),
          VSpace.md(),
          // Google Sign In (Secondary on iOS)
          _buildGoogleSignInButton(context, isPrimary: false),
        ] else ...[
          // Google Sign In (Only option on Android)
          _buildGoogleSignInButton(context, isPrimary: true),
        ],
      ],
    );
  }

  Widget _buildAppleSignInButton(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AppleSignInLoading;

        return BanaButton(
          text: isLoading ? 'Đang đăng nhập...' : 'Đăng nhập bằng Apple',
          onPressed: isLoading
            ? null
            : () => context.read<AuthCubit>().signInWithApple(),
          type: BanaButtonType.primary,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          isFullWidth: true,
          isLoading: isLoading,
          icon: isLoading ? null : Icons.apple,
          size: context.responsive(
            ResponsiveValue(
              mobile: BanaButtonSize.medium,
              tablet: BanaButtonSize.large,
              desktop: BanaButtonSize.large,
            ),
          ),
        );
      },
    );
  }

  Widget _buildGoogleSignInButton(BuildContext context, {required bool isPrimary}) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is GoogleSignInLoading;

        return BanaButton(
          text: isLoading ? 'Đang đăng nhập...' : 'Đăng nhập bằng Google',
          onPressed: isLoading
            ? null
            : () => context.read<AuthCubit>().signInWithGoogle(),
          type: isPrimary ? BanaButtonType.primary : BanaButtonType.secondary,
          isFullWidth: true,
          isLoading: isLoading,
          icon: isLoading ? null : Icons.g_mobiledata, // Placeholder for Google icon
          size: context.responsive(
            ResponsiveValue(
              mobile: BanaButtonSize.medium,
              tablet: BanaButtonSize.large,
              desktop: BanaButtonSize.large,
            ),
          ),
        );
      },
    );
  }





  Widget _buildLegalText(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
        ).copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        children: [
          const TextSpan(text: 'Bằng việc tiếp tục, bạn đồng ý với '),
          TextSpan(
            text: 'Điều khoản Dịch vụ',
            style: TextStyle(
              color: BanaColors.primary,
              decoration: TextDecoration.underline,
            ),
            // Note: Add GestureRecognizer for tap handling in real implementation
          ),
          const TextSpan(text: ' và '),
          TextSpan(
            text: 'Chính sách Bảo mật',
            style: TextStyle(
              color: BanaColors.primary,
              decoration: TextDecoration.underline,
            ),
            // Note: Add GestureRecognizer for tap handling in real implementation
          ),
          const TextSpan(text: ' của BanaChef.'),
        ],
      ),
    );
  }

  /// Show the auth bottom sheet
  static Future<T?> show<T>(
    BuildContext context, {
    VoidCallback? onAuthSuccess,
    VoidCallback? onTermsPressed,
    VoidCallback? onPrivacyPressed,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AuthBottomSheet(
        onAuthSuccess: onAuthSuccess,
        onTermsPressed: onTermsPressed,
        onPrivacyPressed: onPrivacyPressed,
      ),
    );
  }
}
