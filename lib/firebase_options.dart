// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCtWix5ARWLCoqOmT1QWCtcwRi4yNQpQTM',
    appId: '1:196258628916:android:e36e18c4ff373e4437d161',
    messagingSenderId: '196258628916',
    projectId: 'banachef-f03aa',
    storageBucket: 'banachef-f03aa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBx6MZ8BvEfnmflWRkyDglOemiMRsOcJzU',
    appId: '1:196258628916:ios:803380f7f1cab06337d161',
    messagingSenderId: '196258628916',
    projectId: 'banachef-f03aa',
    storageBucket: 'banachef-f03aa.firebasestorage.app',
    iosBundleId: 'banana.care.ai.banachef',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAGer1M_F28Y1Mq2y84l5nv_segQeZYHxg',
    appId: '1:196258628916:web:828349fa2dee075537d161',
    messagingSenderId: '196258628916',
    projectId: 'banachef-f03aa',
    authDomain: 'banachef-f03aa.firebaseapp.com',
    storageBucket: 'banachef-f03aa.firebasestorage.app',
    measurementId: 'G-XFGNFDZRQQ',
  );
}
