name: banachef
description: "A new Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  intl: ^0.19.0

  # State Management
  flutter_bloc: ^8.1.6

  # Dependency Injection
  get_it: ^8.0.2
  injectable: ^2.5.0

  # Navigation
  auto_route: ^10.1.0+1

  # Network
  dio: ^5.7.0
  internet_connection_checker: ^3.0.1

  # Local Storage
  path_provider: ^2.1.5
  shared_preferences: ^2.3.3
  flutter_secure_storage: ^9.2.2

  # Database
  drift: ^2.20.3
  sqlite3_flutter_libs: ^0.5.24

  # Utilities
  logger: ^2.5.0
  equatable: ^2.0.7
  path: ^1.9.0

  # Assets & UI
  flutter_svg: ^2.0.10+1
  lottie: ^3.1.2
  wheel_picker: ^0.2.1

  # Code Generation
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1
  crypto: ^3.0.3

  # Firebase
  firebase_core: ^2.24.2
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.5.7

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  injectable_generator: ^2.6.2
  flutter_gen_runner: ^5.7.0
  auto_route_generator: ^10.1.0

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4

  # Database Code Generation
  drift_dev: ^2.20.1
  mockito: ^5.4.6

flutter:
  uses-material-design: true

  assets:
    - assets/images/app/
    - assets/lottie/
    - assets/icons/
  fonts:
    - family: Nunito
      fonts:
        - asset: assets/fonts/nunito/Nunito-Regular.ttf
        - asset: assets/fonts/nunito/Nunito-Bold.ttf
          weight: 700

    - family: Inter
      fonts:
        - asset: assets/fonts/inter/Inter-Regular.otf
        - asset: assets/fonts/inter/Inter-Medium.otf
          weight: 500
        - asset: assets/fonts/inter/Inter-SemiBold.otf
          weight: 600
        - asset: assets/fonts/inter/Inter-Bold.otf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Gen Configuration
flutter_gen:
  output: lib/shared/assets/ # Output directory
  line_length: 80 # Optional, default is 80

  # Optional
  integrations:
    flutter_svg: true
    rive: false
    lottie: true

  assets:
    enabled: true
    outputs:
      package_parameter_enabled: false
      style: dot-delimiter # Optional, default is snake-case

  fonts:
    enabled: true

  colors:
    enabled: false
